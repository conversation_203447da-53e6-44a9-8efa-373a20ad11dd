// ❌ KHÔNG dùng "use client" ở đây
import "antd/dist/reset.css";
import '@/styles/globals.css';
import "jsvectormap/dist/jsvectormap.css";
import "flatpickr/dist/flatpickr.min.css";
import "@/lib/fontawesome";
import "@/css/satoshi.css";

import "@/css/style.css";


import AuthGuard from "@/components/AuthGuard";
import { BreadcrumbProvider } from "@/context/BreadcrumbContext";
import { LoaderProvider } from "@/context/LoaderContext";
import Breadcrumb from "@/components/Breadcrumb";


export const metadata = {
  title: "Web admin cms",
  description: "App Description",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body suppressHydrationWarning={true}>
        <AuthGuard>
          <LoaderProvider>
            <BreadcrumbProvider>
          
              <div className="dark:bg-boxdark-2 dark:text-bodydark">
                {children}
              </div>
            </BreadcrumbProvider>
          </LoaderProvider>
        </AuthGuard>
      </body>
    </html>
  );
}
