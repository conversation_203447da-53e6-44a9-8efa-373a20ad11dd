'use client';

import React, { createContext, useContext, useState } from 'react';

type BreadcrumbContextType = {
  selectedClass: string | null;
  selectedMenu: string | null;
  setBreadcrumb: (cls: string, menu: string) => void;
};

const BreadcrumbContext = createContext<BreadcrumbContextType | undefined>(undefined);

export const BreadcrumbProvider = ({ children }: { children: React.ReactNode }) => {
  const [selectedClass, setSelectedClass] = useState<string | null>(null);
  const [selectedMenu, setSelectedMenu] = useState<string | null>(null);

  const setBreadcrumb = (cls: string, menu: string) => {
    setSelectedClass(cls);
    setSelectedMenu(menu);
  };

  return (
    <BreadcrumbContext.Provider value={{ selectedClass, selectedMenu, setBreadcrumb }}>
      {children}
    </BreadcrumbContext.Provider>
  );
};

export const useBreadcrumb = () => {
  const context = useContext(BreadcrumbContext);
  if (!context) throw new Error('useBreadcrumb must be used within a BreadcrumbProvider');
  return context;
};
