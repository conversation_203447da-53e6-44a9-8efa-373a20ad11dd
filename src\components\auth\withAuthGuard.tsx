// hoc/withAuthGuard.tsx
"use client";

import React from "react";
import { usePathname, useRouter } from "next/navigation";
import { hasAccessToPath } from "./checkAccess";
import { useGrantedRoles } from "@/hooks/useGrantedRoles";

export const withAuthGuard = (Component: React.ComponentType) => {
  return function ProtectedComponent(props: any) {
    const { roles, loading } = useGrantedRoles();
    const pathname = usePathname();
    const router = useRouter();
    if (loading) return <div><PERSON><PERSON> kiểm tra quyền truy cập...</div>;

    const access = hasAccessToPath(roles, pathname);
    

    if (!access) {
      router.push("/unauthorized");
      return null;
    }

    return <Component {...props} />;
  };
};
