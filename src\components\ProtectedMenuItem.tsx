// components/ProtectedMenuItem.tsx
import { ReactNode } from 'react';

interface ProtectedMenuItemProps {
  roles: string[];
  requiredRoles: string[]; // danh sách role yêu cầu
  children: ReactNode;
}

const ProtectedMenuItem = ({ roles, requiredRoles, children }: ProtectedMenuItemProps) => {
  const hasAccess = requiredRoles.some(role => roles.includes(role));
  return hasAccess ? <>{children}</> : null;
};

export default ProtectedMenuItem;
