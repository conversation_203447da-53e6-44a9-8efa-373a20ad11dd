import axios from '@/services/axios';
import API_ROUTES from '@/constants/api-routes';
import { CreateUser, CreateUserRequest, PatchUsersEnableRequest, UpdateMeRequest, UpdatePasswordRequest, UpdateUserRequest, UpdateUserWithFileRequest, UserItem, VerifyEmailRequest } from '@/types/user/user';
import { ApiResponse, PaginationData } from '@/types/common/api-response';
import { QueryParams } from '@/types/common/query-param';

const User_API = API_ROUTES.USER;

export const getUser = (params: QueryParams) => {
  return axios.get<ApiResponse<PaginationData<UserItem>>>(User_API, { params });
};

export const getUserById = (id: number | string) => {
  return axios.get<ApiResponse<UserItem>>(`${User_API}/${id}`);
};

export const createUser = (data: Partial<CreateUser>) => {
  return axios.post<ApiResponse<CreateUser>>(User_API, data, {
    headers: { 'Content-Type': 'application/json' },
  });
};

export const updateUser = (id: number | string, payload: UpdateUserRequest) => {
  return axios.put<ApiResponse<UpdateUserRequest>>(`${User_API}/${id}`, payload, {
    headers: {
      'Content-Type': 'application/json',
    },
  });
};

export const updateUserWithFile = (
  id: number | string,
  payload: UpdateUserWithFileRequest
) => {
  const formData = new FormData();
  formData.append('PhoneNumber', payload.phoneNumber);
  formData.append('FirstName', payload.firstName);
  formData.append('LastName', payload.lastName);
  formData.append('Address', payload.address);
  formData.append('RoleNames', payload.roleNames);
  if (payload.avatarFile) {
    formData.append('AvatarFile', payload.avatarFile);
  }

  return axios.put<ApiResponse<UserItem>>(`${User_API}/with-file/${id}`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
};

export const updateMyInfo = (payload: UpdateMeRequest) => {
  return axios.put<ApiResponse<UserItem>>(`${User_API}/me`, payload, {
    headers: {
      'Content-Type': 'application/json',
    },
  });
};

export const patchUserPassword = (
  id: number | string,
  payload: UpdatePasswordRequest
) => {
  return axios.patch<ApiResponse<null>>(`${User_API}/${id}/password`, payload, {
    headers: {
      'Content-Type': 'application/json',
    },
  });
};

export const patchUserMaxToken = (id: number | string, tokenMaxCount: number) => {
  return axios.patch<ApiResponse<UserItem>>(
    `${User_API}/${id}/max-token`,
    null, // PATCH body là null (vì dùng query param)
    {
      params: {
        tokenMaxCount,
      },
    }
  );
};

export const patchUserEnableStatus = (id: number | string, isEnable: boolean) => {
  return axios.patch<ApiResponse<UserItem>>(
    `${User_API}/${id}/enable`,
    null, // No body, using query string
    {
      params: { isEnable },
    }
  );
};

export const patchUsersEnable = (payload: PatchUsersEnableRequest) => {
  return axios.patch<ApiResponse<null>>(`${User_API}/enables`, payload, {
    headers: {
      'Content-Type': 'application/json',
    },
  });
};

export const verifyUserEmail = (id: number | string, payload: VerifyEmailRequest) => {
  return axios.patch<ApiResponse<UserItem>>(`${User_API}/${id}/verify-email`, payload, {
    headers: {
      'Content-Type': 'application/json',
    },
  });
};

export const deleteUsersByIds = (userIds: number[]) => {
  return axios.post<ApiResponse<null>>(`${User_API}/delete-all`, userIds, {
    headers: {
      'Content-Type': 'application/json',
    },
  });
};

export const getUserProfile = () => {
  return axios.get<ApiResponse<UserItem>>(`${User_API}/profile`);
};

export const deleteUser = (id: number | string) => {
  return axios.delete<ApiResponse<null>>(`${User_API}/${id}`);
};
