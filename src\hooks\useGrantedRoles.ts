// hooks/useGrantedRoles.ts
'use client';

import { useEffect, useState } from 'react';
import { getGrantedRolesForCurrentUser } from '@/services/role/role'; // Adjust the import path as necessary
export function useGrantedRoles() {
  const [roles, setRoles] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchRoles = async () => {
      try {
        const response = await getGrantedRolesForCurrentUser();
        setRoles(response.data?.data?.map((r: any) => r.roleName) || []); // Lấy tên role
        console.log('Granted roles:', response.data?.data);
        
      } catch (err) {
        console.error('Failed to fetch roles:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchRoles();
  }, []);

  return { roles, loading };
}
