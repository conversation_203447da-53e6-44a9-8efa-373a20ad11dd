"use client";

import React from "react";
import { Pagination as AntdPagination, Select } from "antd";

const { Option } = Select;

interface PaginationProps {
  currentPage: number;
  pageSize: number;
  totalItems: number;
  onPageChange: (page: number) => void;
  onPageSizeChange: (size: number) => void;
}

const Pagination: React.FC<PaginationProps> = ({
  currentPage,
  pageSize,
  totalItems,
  onPageChange,
  onPageSizeChange,
}) => {
  return (
    <div className="mt-6 flex flex-col items-center justify-between gap-4 sm:flex-row">
      <div className="text-sm text-gray-600">
        Tổng số: <span className="font-semibold">{totalItems}</span> bản ghi
      </div>

      <div className="flex items-center gap-4">
        <AntdPagination
          current={currentPage}
          total={totalItems}
          pageSize={pageSize}
          onChange={onPageChange}
          showSizeChanger={false}
          showQuickJumper
        />

        <Select
          value={pageSize}
          onChange={onPageSizeChange}
          size="middle"
          className="w-28"
        >
          <Option value={5}>5 / trang</Option>
          <Option value={10}>10 / trang</Option>
          <Option value={20}>20 / trang</Option>
          <Option value={50}>50 / trang</Option>
        </Select>
      </div>
    </div>
  );
};

export default Pagination;
