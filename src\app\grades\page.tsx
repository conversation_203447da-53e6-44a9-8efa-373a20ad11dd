"use client";

import React, { useEffect, useState } from "react";
import DefaultLayout from "@/components/Layouts/DefaultLayout";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faPlus,
  faRedo,
  faSearch,
  faEdit,
  faTrash,
  faTimes,
  faFileExport,
  faFileImport,
  faFileExcel,
  faDownload,
} from "@fortawesome/free-solid-svg-icons";
import { getGrade, deleteGrade, saveGrade } from "@/services/grade/grade";
import { GradeItem } from "@/types/grade/gradeItem";
import Pagination from "@/components/common/Pagination";
import { Button, Modal, Form, Input, Select, notification } from "antd";
import { useBreadcrumb } from "@/context/BreadcrumbContext";
import { time } from "node:console";
import { useLoader } from "@/context/LoaderContext";
import * as XLSX from "xlsx";
import AdminLayout from "@/components/Layouts/AdminLayout";
import { withAuthGuard } from "@/components/auth/withAuthGuard";

const initialForm: Partial<GradeItem> = {
  name: "",
  code: "",
  description: ""
};

function GradesPage() {
  const { setLoading } = useLoader();
  const { setBreadcrumb } = useBreadcrumb();
  const [data, setData] = useState<GradeItem[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [inputSearchTerm, setInputSearchTerm] = useState("");
  const [form, setForm] = useState<Partial<GradeItem>>(initialForm);
  const [editingId, setEditingId] = useState<number | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [antdForm] = Form.useForm();
  const [selectedGrades, setSelectedGrades] = useState<number[]>([]);

  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalItems, setTotalItems] = useState(0);

  const [previewImportData, setPreviewImportData] = useState<GradeItem[]>([]);
  const [isPreviewModalOpen, setIsPreviewModalOpen] = useState(false);




  const fetchGrades = async () => {
    try {
      setLoading(true);
      const res = await getGrade({
        SearchTerm: searchTerm,
        Page: currentPage,
        ItemPerPage: pageSize,
      });
      setData(res.data.data?.items || []);
      setTotalItems(res.data.data?.total || 0);
      setSelectedGrades([]);
    } catch (err) {
      console.error("Lỗi tải danh sách cấp lớp học", err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    setBreadcrumb("Quản lý cấp lớp học", "Danh sách cấp lớp học");
  }, []);

  // Reset currentPage về 1 khi searchTerm thay đổi
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm]);

  // Debounce fetchGrades khi searchTerm, currentPage hoặc pageSize thay đổi
  useEffect(() => {
    const timeout = setTimeout(() => {
      fetchGrades();
    }, 300); // Debounce 300ms

    return () => clearTimeout(timeout); // Clear timeout nếu thay đổi trong thời gian chờ
  }, [searchTerm, currentPage, pageSize]);

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    setSearchTerm(inputSearchTerm);
    setCurrentPage(1); // Đặt lại trang đầu tiên

    // Delay gọi fetchGrades sau khi setState hoàn tất
    setTimeout(() => {
      fetchGrades(); // fetchGrades sẽ gọi theo currentPage = 1
    }, 0);
  };

  const resetSearch = async () => {
    setSearchTerm("");
    setCurrentPage(1);
    await fetchGrades();
  };

  const handleSubmit = async () => {
    try {
      const values = await antdForm.validateFields();
      setLoading(true);
      await saveGrade(editingId ? { ...values, id: editingId } : values);
      notification.success({
        message: editingId ? "Cập nhật thành công" : "Thêm mới thành công",
      });
      setCurrentPage(1);
      await fetchGrades();
      closeModal();
    } catch (errorInfo) {
      console.log("Validation Failed:", errorInfo);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    Modal.confirm({
      title: "Bạn có chắc chắn muốn xoá?",
      okText: "Xoá",
      okType: "danger",
      cancelText: "Hủy",
      onOk: async () => {
        try {
          setLoading(true);
          await deleteGrade(id);
          notification.success({ message: "Xoá thành công" });
          setCurrentPage(1);
          await fetchGrades();
        } catch {
          notification.error({ message: "Lỗi khi xoá cấp lớp học" });
        } finally {
          setLoading(false);
        }
      },
    });
  };

  const openModal = (Grade?: GradeItem) => {
    if (Grade) {
      setForm(Grade);
      setEditingId(Grade.id);
      antdForm.setFieldsValue(Grade); // ✅ Set form field values
    } else {
      setForm(initialForm);
      setEditingId(null);
      antdForm.resetFields(); // ✅ Reset fields
    }
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setForm(initialForm);
    setEditingId(null);
    setIsModalOpen(false);
  };

  const handleDeleteMultiple = async () => {
    Modal.confirm({
      title: `Bạn có chắc chắn muốn xoá ${selectedGrades.length} cấp lớp học?`,
      okText: "Xoá",
      okType: "danger",
      cancelText: "Hủy",
      onOk: async () => {
        try {
          setLoading(true);
          for (const id of selectedGrades) {
            await deleteGrade(id);
          }
          notification.success({ message: "Xoá thành công" });
          setSelectedGrades([]);
          setCurrentPage(1);
          await fetchGrades();
        } catch {
          notification.error({ message: "Lỗi khi xoá nhiều cấp lớp học" });
        } finally {
          setLoading(false);
        }
      },
    });
  };


  const handleExportExcel = () => {
    const exportData = data.map(({ name, code, description }) => ({
      name, code, description
    }));

    const worksheet = XLSX.utils.json_to_sheet(exportData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "DanhSachToChuc");

    XLSX.writeFile(workbook, "danh_sach_to_chuc.xlsx");
  };


  return (
    <AdminLayout>
      <div className="font-sans mx-auto rounded-md bg-white p-6">
        {/* Search + Add Form */}
        <div className="mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <form className="flex w-full sm:w-auto" onSubmit={handleSearch}>
            <div className="relative flex-grow">
              <input
                id="search"
                type="search"
                value={searchTerm}
                onChange={(e) => setInputSearchTerm(e.target.value)}
                placeholder="Tìm kiếm theo tên"
                className="w-full rounded-l-md border border-gray-300 py-2 pl-10 pr-12 text-gray-400 placeholder-gray-400 focus:border-red-600 focus:outline-none focus:ring-2 focus:ring-red-600"
              />
              <span className="absolute left-3 top-1/2 -translate-y-1/2 text-lg text-gray-400">
                <FontAwesomeIcon icon={faSearch} />
              </span>
              <button
                type="reset"
                onClick={resetSearch}
                className="absolute right-0 top-1/2 -translate-y-1/2 rounded-r-md border-l border-gray-300 bg-white px-3 py-2 text-gray-600 hover:text-gray-900"
              >
                <FontAwesomeIcon icon={faTimes} />
              </button>
            </div>
            <button
              type="submit"
              className="ml-2 flex items-center justify-center rounded-md bg-red-600 px-4 py-2 text-white hover:bg-red-700"
            >
              <FontAwesomeIcon icon={faSearch} />
            </button>
          </form >

          <div className="flex items-end gap-2">
            {selectedGrades.length > 0 && (
              <>
                <Button
                  danger
                  onClick={handleDeleteMultiple}
                  className="rounded-none"
                >
                  Xoá đã chọn ({selectedGrades.length})
                </Button>
              </>
            )}

            <Button
              className="rounded-none bg-blue-600 text-white"
              type="primary"
              icon={<FontAwesomeIcon icon={faPlus} />}
              onClick={() => openModal()}
            >
              Thêm cấp lớp học
            </Button>

            <Button
              className="rounded-none  bg-success text-white"
              onClick={handleExportExcel}
              icon={<FontAwesomeIcon icon={faDownload} />}
            >
              Xuất Excel
            </Button>
          </div >
        </div >

        {/* Table */}
        < table className="w-full table-auto border-collapse text-sm" >
          <thead>
            <tr className="bg-gray-200 font-semibold text-gray-900">
              <th className="w-[60px] border px-2 py-3 text-center">
                <div className="flex items-center justify-center gap-1">
                  <input
                    type="checkbox"
                    checked={selectedGrades.length === data.length}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedGrades(data.map((item) => item.id));
                      } else {
                        setSelectedGrades([]);
                      }
                    }}
                    className="accent-red-600"
                  />
                </div>
              </th>
              <th className="border px-4 py-3">Mã cấp lớp học</th>
              <th className="border px-4 py-3">Tên cấp lớp học</th>
              <th className="border px-4 py-3">Mô tả</th>
              <th className="w-[100px] border px-2 py-3 text-right">
                Chức năng
              </th>
            </tr >
          </thead >
          <tbody>
            {data.map((item, index) => (
              <tr
                key={item.id}
                className={`${index % 2 === 0 ? "bg-white" : "bg-gray-50"} hover:bg-red-50`}
              >
                <td className="w-[60px] border px-2 py-3 text-center">
                  <div className="flex items-center justify-center gap-1">
                    <span className="mr-4 text-xs font-bold">
                      {(currentPage - 1) * pageSize + index + 1}
                    </span>
                    <input
                      type="checkbox"
                      checked={selectedGrades.includes(item.id)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedGrades([...selectedGrades, item.id]);
                        } else {
                          setSelectedGrades(
                            selectedGrades.filter((id) => id !== item.id),
                          );
                        }
                      }}
                      className="accent-red-600"
                    />
                  </div>
                </td>
                <td className="border px-4 py-3 font-semibold">{item.code}</td>
                <td className="border px-4 py-3 font-semibold">{item.name}</td>
                <td className="border px-4 py-3">{item.description}</td>
                <td className="border px-2 py-3 text-right">
                  <div className="flex justify-end gap-2">
                    <button
                      onClick={() => openModal(item)}
                      className="text-blue-600 hover:text-blue-800"
                      title="Sửa"
                    >
                      <FontAwesomeIcon icon={faEdit} />
                    </button>
                    <button
                      onClick={() => handleDelete(item.id)}
                      className="text-red-600 hover:text-red-800"
                      title="Xoá"
                    >
                      <FontAwesomeIcon icon={faTrash} />
                    </button>
                  </div>
                </td>
              </tr >
            ))
            }
          </tbody >
        </table >
        <Pagination
          currentPage={currentPage}
          pageSize={pageSize}
          totalItems={totalItems}
          onPageChange={(page) => setCurrentPage(page)}
          onPageSizeChange={(size) => {
            setPageSize(size);
            setCurrentPage(1); // reset về trang 1 khi đổi size
          }}
        />
      </div >

      <Modal
        title={editingId ? "Cập nhật" : "Thêm mới"}
        open={isModalOpen}
        onCancel={closeModal}
        footer={[
          <Button key="back" onClick={closeModal}>
            Hủy
          </Button>,
          <Button
            key="submit"
            type="primary"
            className="bg-blue-500 hover:bg-blue-600 text-white"
            onClick={handleSubmit}
          >
            {editingId ? "Cập nhật" : "Thêm mới"}
          </Button>,
        ]}
      >
        <Form layout="vertical" form={antdForm}>
          <Form.Item
            label="Mã cấp lớp học"
            name="code"
            rules={[{ required: true, message: "Vui lòng nhập tên mã" }]}
          >
            <Input placeholder="Nhập tên mã" />
          </Form.Item>
          <Form.Item
            label="Tên cấp lớp học"
            name="name"
            rules={[{ required: true, message: "Vui lòng nhập tên cấp lớp học" }]}
          >
            <Input placeholder="Nhập tên cấp lớp học" />
          </Form.Item>


          <Form.Item
            label="Mô tả"
            name="description"

          >
            <Input placeholder="Nhập mô tả" />
          </Form.Item>
        </Form>
      </Modal>
    </AdminLayout>
  );
}


export default withAuthGuard(GradesPage);
