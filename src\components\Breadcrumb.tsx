'use client';

import { useBreadcrumb } from '@/context/BreadcrumbContext';
import { ChevronRightIcon } from '@heroicons/react/20/solid';
export default function Breadcrumb() {
  const { selectedClass, selectedMenu } = useBreadcrumb();

  if (!selectedClass || !selectedMenu) return null;

  return (
    <nav className="mb-6" aria-label="Breadcrumb">
      <ol className="flex flex-wrap items-center space-x-2 text-sm text-gray-600">
        <li className="flex items-center">
          <span className="text-gray-500 hover:text-gray-700">{selectedClass}</span>
          <ChevronRightIcon className="mx-2 h-4 w-4 text-gray-400" aria-hidden="true" />
        </li>
        <li className="font-semibold text-red-600">{selectedMenu}</li>
      </ol>
    </nav>
  );
}
