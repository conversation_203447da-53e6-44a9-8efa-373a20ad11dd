// src/components/AuthGuard.tsx
'use client';

import { useEffect, useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import Cookies from 'js-cookie';

const PUBLIC_PATHS = ['/login', '/logout'];

export default function AuthGuard({ children }: { children: React.ReactNode }) {
  const router = useRouter();
  const pathname = usePathname();
  const [isReady, setIsReady] = useState(false);

  useEffect(() => {
    const accessToken = Cookies.get('accessToken') || sessionStorage.getItem('accessToken');
    const isPublic = PUBLIC_PATHS.includes(pathname);

    if (!accessToken && !isPublic) {
      router.replace('/login');
    } else {
      setIsReady(true);
    }
  }, [pathname]);

  if (!isReady) return null;

  return <>{children}</>;
}
