export interface UserItem {
  id: number;
  userName: string;
  email: string;
  phoneNumber: string;
  firstName: string;
  lastName: string;
  address: string;
  token: string | null;
  total: number;
  maxTokenCount: number;
  balance: number;
  isEnabled: boolean;
  imageUrl: string | null;
  organizationId: number | null;
  classId: number | null;
  createdTime: string;
  updatedTime: string;
  lastLoginTime: string;
}

export interface CreateUserRequest {
  userName: string;
  email: string;
  phoneNumber: string;
  firstName: string;
  lastName: string;
  address: string;
  password: string;
  roleNames: string;
  imageUrl?: string;
  avatarFile?: File | null;
}

export interface CreateUser {
  userName: string;
  email: string;
  password: string;
  phoneNumber: string;
  firstName: string;
  lastName: string;
  address: string;
  imageUrl: string;
  roleNames: string[];
  organizationId: number;
  classId: number;
}



export interface UpdateUserRequest {
  phoneNumber: string;
  firstName: string;
  lastName: string;
  address: string;
  organizationId: number;
  classId: number;
  roleNames: string[];
}

export interface UpdateUserWithFileRequest {
  phoneNumber: string;
  firstName: string;
  lastName: string;
  address: string;
  roleNames: string;
  avatarFile?: File | null; // nếu cần gửi file, thêm trường này
}

export interface UpdateMeRequest {
  phoneNumber: string;
  firstName: string;
  lastName: string;
  address: string;
  roleNames: string[];
}

export interface UpdatePasswordRequest {
  newPassword: string;
}

export interface PatchUsersEnableRequest {
  userIds: number[];
  status: boolean;
}

export interface VerifyEmailRequest {
  code: string;
}