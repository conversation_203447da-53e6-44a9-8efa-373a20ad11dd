import { useLoader } from "@/context/LoaderContext";
import { getGrade } from "@/services/grade/grade";
import { useEffect, useState } from "react";
import Pagination from "@/components/common/Pagination";
import { WeekItem } from "@/types/week/week";
import { getWeek } from "@/services/week/week";
import { SessionItem } from "@/types/session/session";
import { getSession } from "@/services/session/session";
import { CourseItem } from "@/types/course/course";
import { getCourse } from "@/services/course/course";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faTimes } from "@fortawesome/free-solid-svg-icons";
import { ExerciseTypeEnum } from "@/types/exercise/exercise";
import { GradeItem } from "@/types/grade/gradeItem";

export default function TextInputForm({
  onSubmit,
}: {
  onSubmit?: (data: any) => void;
}) {
  const { setLoading } = useLoader();
  const [searchTerm, setSearchTerm] = useState("");
  const [inputSearchTerm, setInputSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalItems, setTotalItems] = useState(0);

  const [searchColumn, setSearchColumn] = useState<string | undefined>();
  const [orderColumn, setOrderColumn] = useState<string | undefined>();
  const [orderDirection, setOrderDirection] = useState<0 | 1 | null>(0); // 0: ASC, 1: DESC
  const [gradeListData, getGradeListData] = useState<GradeItem[]>([]);
  const [courseList, getCourseListData] = useState<CourseItem[]>([]);
  const [weekList, getWeekData] = useState<WeekItem[]>([]);
  const [sessionList, getSessionListData] = useState<SessionItem[]>([]);

  const fetchgradeListData = async () => {
    try {
      setLoading(true);

      const res = await getGrade({
        Page: currentPage,
        ItemPerPage: pageSize,
        SearchTerm: searchTerm,
        SearchColumn: searchColumn,
        OrderColumn: orderColumn,
        Direction: orderDirection,
      });

      const items = res.data.data?.items || [];
      getGradeListData(items);
      console.log("Danh sách lớp:", items);
      setTotalItems(res.data.data?.total || 0);
    } catch (err) {
      console.error("Lỗi tải danh sách Lớp", err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchgradeListData();
  }, []);


  const fetchCourseListData = async () => {
    try {
      setLoading(true);
      const res = await getCourse({
        Page: currentPage,
        ItemPerPage: pageSize,
        SearchTerm: searchTerm,
        SearchColumn: searchColumn,
        OrderColumn: orderColumn,
        Direction: orderDirection,
      });

      const items = res.data.data?.items || [];
      getCourseListData(items);
      console.log("Danh sách môn học:", items);
      setTotalItems(res.data.data?.total || 0);
    } catch (err) {
      console.error("Lỗi tải danh sách môn học", err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCourseListData();
  }, []);

  const fetchWeekListData = async (courseId: number | string) => {
    try {
      setLoading(true);

      const res = await getWeek({
        Page: currentPage,
        ItemPerPage: pageSize,
        SearchTerm: String(courseId),
        SearchColumn: "CourseId",
        OrderColumn: orderColumn,
        Direction: orderDirection,
      });

      const items = res.data.data?.items || [];
      getWeekData(items);
      console.log("Danh sách tuần:", items);
      setTotalItems(res.data.data?.total || 0);
    } catch (err) {
      console.error("Lỗi tải danh sách tuần", err);
    } finally {
      setLoading(false);
    }
  };


  const fetchSessionListData = async (weekId: number | string) => {
    try {
      setLoading(true);

      const res = await getSession({
        Page: currentPage,
        ItemPerPage: pageSize,
        SearchTerm: String(weekId),    // 👉 Giá trị WeekId
        SearchColumn: "WeekId",        // 👉 Trường cần tìm là WeekId
        OrderColumn: orderColumn,
        Direction: orderDirection,
      });

      const items = res.data.data?.items || [];
      getSessionListData(items);
      console.log("Danh sách buổi:", items);
      setTotalItems(res.data.data?.total || 0);
    } catch (err) {
      console.error("Lỗi tải danh sách buổi", err);
    } finally {
      setLoading(false);
    }
  };


  const [formData, setFormData] = useState({
    Title: "",
    Code: "",
    GradeId: "",
    CourseId: "",
    WeekId: "",
    LessonId: "",
    TypeId: ExerciseTypeEnum.TextInput, // Giả sử đây là loại bài tập Trắc nghiệm
    Metadata: "",
    QuestionData: "",
    CorrectAnswers: "",
    Item: {
      questionText: "",
      expectedAnswerText: "",
      explanation: "",
      medias: [] as {
        id: string;
        type: number;
        displayType: number;
        content: string;
        filePath: string;
        displayOrder: number;
        file: string;
      }[],
      answers: [] as {
        id: string;
        groupKey: string;
        isCorrect: boolean;
        filePath: string;
        orderIndex: number;
        displayOrder: number;
        content: string;
        explanation: string;
        matchKey: string;
        type: number;
        file: string;
      }[],
      matchingPairs: [] as { left: string; right: string }[],
      displayType: 1,
      displayDirection: 0,
      sentenceOrderingJson: "",
    },
  });
  const [showPreview, setShowPreview] = useState(false);

  const addQuestionItemTracNghiem = (type: string) => {
    const mediaType = { text: 1, image: 2, audio: 3, video: 4 }[type] || 1;
    const newMedia = {
      id: `q-${Date.now()}`,
      type: mediaType,
      displayType: 1,
      content: type === "text" ? "" : "",
      filePath: "",
      displayOrder: formData.Item.medias.length + 1,
      file: "",
    };
    setFormData({
      ...formData,
      Item: {
        ...formData.Item,
        medias: [...formData.Item.medias, newMedia],
      },
    });
  };

  const addAnswerItemTracNghiem = () => {
    const newAnswer = {
      id: `a-${Date.now()}`,
      groupKey: "",
      isCorrect: false,
      filePath: "",
      orderIndex: formData.Item.answers.length + 1,
      displayOrder: formData.Item.answers.length + 1,
      content: "",
      explanation: "",
      matchKey: "",
      type: 1,
      file: "",
    };
    setFormData({
      ...formData,
      Item: {
        ...formData.Item,
        answers: [...formData.Item.answers, newAnswer],
      },
    });
  };

  const removeQuestionItemTracNghiem = (id: string) => {
    const updatedMedias = formData.Item.medias
      .filter((q) => q.id !== id)
      .map((q, index) => ({ ...q, displayOrder: index + 1 }));
    setFormData({
      ...formData,
      Item: { ...formData.Item, medias: updatedMedias },
    });
  };

  const removeAnswerItemTracNghiem = (id: string) => {
    const updatedAnswers = formData.Item.answers
      .filter((a) => a.id !== id)
      .map((a, index) => ({
        ...a,
        orderIndex: index + 1,
        displayOrder: index + 1,
      }));
    setFormData({
      ...formData,
      Item: { ...formData.Item, answers: updatedAnswers },
    });
  };

  const handleQuestionChangeTracNghiem = (
    id: string,
    field: string,
    value: string,
  ) => {
    setFormData({
      ...formData,
      Item: {
        ...formData.Item,
        medias: formData.Item.medias.map((q) =>
          q.id === id ? { ...q, [field]: value } : q,
        ),
      },
    });
  };

  const handleAnswerChangeTracNghiem = (
    id: string,
    field: string,
    value: any,
  ) => {
    setFormData({
      ...formData,
      Item: {
        ...formData.Item,
        answers: formData.Item.answers.map((a) =>
          a.id === id ? { ...a, [field]: value } : a,
        ),
      },
    });
  };

  const handleFileChangeTracNghiem = (id: string, file: File | null) => {
    if (file) {
      const reader = new FileReader();
      reader.onload = () => {
        setFormData({
          ...formData,
          Item: {
            ...formData.Item,
            medias: formData.Item.medias.map((q) =>
              q.id === id
                ? { ...q, file: reader.result as string, filePath: file.name }
                : q,
            ),
          },
        });
      };
      reader.readAsDataURL(file);
    }
  };

  const handleRemoveFile = (id: string) => {
    setFormData({
      ...formData,
      Item: {
        ...formData.Item,
        medias: formData.Item.medias.map((q) =>
          q.id === id ? { ...q, file: "", filePath: "" } : q,
        ),
      },
    });
  };

  // const handleSubmitTracNghiem = () => {
  //   const correctAnswers = formData.Items.answers
  //     .filter((a) => a.isCorrect)
  //     .map((a) => a.content)
  //     .join(", ");

  //   const output = {
  //     ...formData,
  //     CorrectAnswers: correctAnswers,
  //     Items: JSON.stringify(formData.Items),
  //     ClassId: parseInt(formData.ClassId) || 0,
  //     WeekId: parseInt(formData.WeekId) || 0,
  //     LessonId: parseInt(formData.LessonId) || 0,
  //     TypeId: formData.TypeId,
  //   };

  //   console.log("Dữ liệu gửi lên API:", JSON.stringify(output, null, 2));

  //   if (onSubmit) onSubmit(output);
  // };

  const handleSubmitTracNghiem = () => {
    const correctAnswers = formData.Item.answers
      .filter((a) => a.isCorrect)
      .map((a) => a.content)
      .join(", ");

    const { CourseId, ...restFormData } = formData;

    const output = {
      ...restFormData,
      CorrectAnswers: correctAnswers,
      Item: JSON.stringify(formData.Item),
      ClassId: parseInt(formData.GradeId) || 0,
      WeekId: parseInt(formData.WeekId) || 0,
      LessonId: parseInt(formData.LessonId) || 0,
      TypeId: formData.TypeId,
      Metadata: JSON.stringify({ createdBy: "user", createdAt: new Date().toISOString() }), // hoặc {} nếu không có gì
      QuestionData: JSON.stringify(formData.Item), // có thể giống Items
    };

    console.log("Dữ liệu gửi lên API:", JSON.stringify(output, null, 2));

    if (onSubmit) onSubmit(output);
  };


  const handlePreviewTracNghiem = () => {
    if (!formData.Code || !formData.Item.questionText) {
      alert("Vui lòng điền đầy đủ thông tin bắt buộc trước khi xem trước!");
      return;
    }
    setShowPreview(true);
  };

  return (
    <div className="flex min-h-screen items-center justify-center">
      <div className="w-full max-w-[1200px]">
        <form className="font-sans space-y-6 p-6 text-[14px] text-gray-900">
          <div className="grid grid-cols-1 gap-x-6 gap-y-4 sm:grid-cols-2 md:grid-cols-6">
            <div>
              <label
                htmlFor="ma-bai"
                className="mb-1 block text-[13px] font-normal text-gray-700"
              >
                Mã bài <span className="text-red-600">*</span>
              </label>
              <input
                id="ma-bai"
                type="text"
                value={formData.Code}
                onChange={(e) =>
                  setFormData({ ...formData, Code: e.target.value })
                }
                className="w-full rounded-md border border-gray-300 px-3 py-2 text-[14px] focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label
                htmlFor="chon-lop"
                className="mb-1 block text-[13px] font-normal text-gray-700"
              >
                Chọn lớp <span className="text-red-600">*</span>
              </label>
              <select
                id="chon-lop"
                value={formData.GradeId}
                onChange={(e) =>
                  setFormData({ ...formData, GradeId: e.target.value })
                }
                className="w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-[14px] focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="" disabled>-- Chọn lớp --</option>
                {gradeListData.map((cls) => (
                  <option key={cls.id} value={cls.id}>
                    {cls.name}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label
                htmlFor="chon-monhoc"
                className="mb-1 block text-[13px] font-normal text-gray-700"
              >
                Chọn môn học <span className="text-red-600">*</span>
              </label>
              <select
                id="chon-monhoc"
                value={formData.CourseId}
                onChange={(e) => {
                  const selectedCourseId = e.target.value;
                  setFormData({
                    ...formData,
                    CourseId: selectedCourseId,
                    WeekId: "", // reset tuần
                    LessonId: "", // reset buổi
                  });

                  if (selectedCourseId) {
                    fetchWeekListData(selectedCourseId);
                    getWeekData([]); // clear week list trước khi gọi
                    getSessionListData([]); // clear session list
                  }
                }}
                className="w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-[14px] focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="" disabled>-- Chọn môn học --</option>
                {courseList.map((course) => (
                  <option key={course.id} value={course.id}>
                    {course.title}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label
                htmlFor="chon-tuan"
                className="mb-1 block text-[13px] font-normal text-gray-700"
              >
                Chọn tuần <span className="text-red-600">*</span>
              </label>
              <select
                id="chon-tuan"
                value={formData.WeekId}
                onChange={(e) => {
                  const selectedWeekId = e.target.value;
                  setFormData({ ...formData, WeekId: selectedWeekId });

                  if (selectedWeekId) {
                    fetchSessionListData(selectedWeekId); // 👉 gọi hàm lấy danh sách buổi
                  }
                }}
                className="w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-[14px] focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="" disabled>-- Chọn tuần --</option>
                {weekList.map((week) => (
                  <option key={week.id} value={week.id}>
                    {week.title}
                  </option>
                ))}
              </select>

            </div>
            <div>
              <label
                htmlFor="chon-buoi"
                className="mb-1 block text-[13px] font-normal text-gray-700"
              >
                Chọn buổi <span className="text-red-600">*</span>
              </label>
              <select
                id="chon-buoi"
                value={formData.LessonId}
                onChange={(e) =>
                  setFormData({ ...formData, LessonId: e.target.value })
                }
                className="w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-[14px] focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="" disabled>-- Chọn buổi --</option>
                {sessionList.map((session) => (
                  <option key={session.id} value={session.id}>
                    {session.sessionName}
                  </option>
                ))}
              </select>

            </div>
          </div>

          <div>
            <label
              htmlFor="de-bai"
              className="mb-1 block text-[13px] font-normal text-gray-700"
            >
              Đề bài <span className="text-red-600">*</span>
            </label>
            <input
              id="de-bai"
              type="text"
              value={formData.Item.questionText}
              onChange={(e) =>
                setFormData({
                  ...formData,
                  Item: { ...formData.Item, questionText: e.target.value },
                })
              }
              className="w-full rounded-md border border-gray-300 px-3 py-2 text-[14px] focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div className="space-y-4 rounded-md border border-gray-300 p-4">
            <div className="text-[13px] font-normal text-gray-700 mb-1">
              Câu hỏi <span className="text-red-600">*</span>
            </div>
            <div className="flex flex-wrap items-center gap-4 text-[14px] font-semibold text-gray-800">
              <label className="flex cursor-pointer items-center gap-2">
                <input
                  type="radio"
                  name="sap-xep"
                  value="0"
                  checked={formData.Item.displayDirection === 0}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      Item: {
                        ...formData.Item,
                        displayDirection: parseInt(e.target.value),
                      },
                    })
                  }
                  className="h-4 w-4 border-gray-300 text-red-600 focus:ring-red-600"
                />
                <span>Hiển thị sắp xếp theo chiều ngang</span>
              </label>
              <label className="flex cursor-pointer items-center gap-2">
                <input
                  type="radio"
                  name="sap-xep"
                  value="1"
                  checked={formData.Item.displayDirection === 1}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      Item: {
                        ...formData.Item,
                        displayDirection: parseInt(e.target.value),
                      },
                    })
                  }
                  className="h-4 w-4 border-gray-300 text-red-600 focus:ring-red-600"
                />
                <span>Hiển thị sắp xếp theo chiều dọc</span>
              </label>
              <button
                type="button"
                onClick={() => addQuestionItemTracNghiem("text")}
                className="ml-auto flex items-center gap-1 rounded bg-blue-600 px-3 py-1 text-[14px] font-semibold text-white hover:bg-blue-700"
              >
                <i className="fas fa-plus"></i> Văn bản
              </button>
              <button
                type="button"
                onClick={() => addQuestionItemTracNghiem("image")}
                className="flex items-center gap-1 rounded bg-sky-500 px-3 py-1 text-[14px] font-semibold text-white hover:bg-sky-600"
              >
                <i className="fas fa-plus"></i> Hình ảnh
              </button>
              <button
                type="button"
                onClick={() => addQuestionItemTracNghiem("audio")}
                className="flex items-center gap-1 rounded bg-green-500 px-3 py-1 text-[14px] font-semibold text-white hover:bg-green-600"
              >
                <i className="fas fa-plus"></i> Audio
              </button>
              <button
                type="button"
                onClick={() => addQuestionItemTracNghiem("video")}
                className="flex items-center gap-1 rounded bg-orange-500 px-3 py-1 text-[14px] font-semibold text-white hover:bg-orange-600"
              >
                <i className="fas fa-plus"></i> Video
              </button>
            </div>
            <div className="mt-3 space-y-3 text-[14px]" id="questionContainer">
              {formData.Item.medias.map((q) => (
                <div key={q.id} className="flex items-center gap-3">
                  <label className="w-[90px] font-semibold capitalize">
                    {{ 1: "text", 2: "image", 3: "audio", 4: "video" }[q.type]}
                  </label>
                  {q.type === 1 ? (
                    <div className="flex flex-grow items-center gap-3">
                      <input
                        type="text"
                        value={q.content}
                        onChange={(e) =>
                          handleQuestionChangeTracNghiem(
                            q.id,
                            "content",
                            e.target.value,
                          )
                        }
                        className="flex-grow rounded-md border px-3 py-2"
                      />
                      <button
                        type="button"
                        onClick={() => removeQuestionItemTracNghiem(q.id)}
                        className="text-red-600 hover:text-red-800"
                        title="Xóa câu hỏi"
                      >
                        <i className="fa fa-trash-alt"></i> Xóa
                      </button>
                    </div>
                  ) : (
                    <div className="flex flex-grow items-center gap-3">
                      <div className="relative inline-block">
                        <input
                          type="file"
                          id={`file-${q.id}`}
                          accept={`${{ 2: "image", 3: "audio", 4: "video" }[q.type]}/*`}
                          onChange={(e) => {
                            if (e.target.files && e.target.files.length > 0) {
                              handleFileChangeTracNghiem(
                                q.id,
                                e.target.files[0],
                              );
                            }
                          }}
                          className="hidden"
                        />
                        <label
                          htmlFor={`file-${q.id}`}
                          className="flex cursor-pointer items-center gap-1 rounded bg-blue-700 px-3 py-1 text-[14px] font-semibold text-white hover:bg-blue-800"
                        >
                          <i className="fas fa-upload"></i> Tải lên
                        </label>
                      </div>
                      {q.filePath && (
                        <div className="flex items-center gap-2">
                          <span className="text-gray-700">{q.filePath}</span>
                          <button
                            type="button"
                            onClick={() => handleRemoveFile(q.id)}
                            className="text-red-600 hover:text-red-800"
                            title="Xóa tệp"
                          >
                            <i className="fas fa-times"></i>
                          </button>
                        </div>
                      )}
                      <button
                        type="button"
                        onClick={() => removeQuestionItemTracNghiem(q.id)}
                        className="text-red-600 hover:text-red-800"
                        title={`Xóa ${{ 2: "hình ảnh", 3: "audio", 4: "video" }[q.type]}`}
                      >
                        <i className="fa fa-trash-alt"></i> Xóa
                      </button>
                    </div>
                  )}
                  <label className="w-[110px] text-right font-semibold">
                    Thứ tự
                  </label>
                  <input
                    type="number"
                    value={q.displayOrder}
                    onChange={(e) =>
                      handleQuestionChangeTracNghiem(
                        q.id,
                        "displayOrder",
                        e.target.value,
                      )
                    }
                    className="w-[60px] rounded-md border px-2 py-1"
                  />
                </div>
              ))}
            </div>
          </div>

          <div className="space-y-4 rounded-md border border-gray-300 p-4">
            <div className="text-[13px] font-normal text-gray-700">
              Đáp án <span className="text-red-600">*</span>
            </div>
            <div className="flex items-center gap-4">
              <label
                htmlFor="loai-dap-an"
                className="w-[90px] text-[14px] font-normal text-gray-700"
              >
                Loại đáp án
              </label>
              <select
                id="loai-dap-an"
                className="flex-grow rounded-md border border-gray-300 bg-white px-3 py-2 text-[14px] focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option selected>Văn bản</option>
              </select>
              <button
                type="button"
                onClick={addAnswerItemTracNghiem}
                className="ml-auto flex items-center gap-1 rounded bg-red-600 px-3 py-1 text-[14px] font-semibold text-white hover:bg-red-700"
              >
                <i className="fas fa-plus"></i> Đáp án
              </button>
            </div>
            <div className="space-y-3" id="answerContainer">
              {formData.Item.answers.map((a) => (
                <div key={a.id} className="flex items-center gap-3">
                  <input
                    type="text"
                    value={a.content}
                    onChange={(e) =>
                      handleAnswerChangeTracNghiem(
                        a.id,
                        "content",
                        e.target.value,
                      )
                    }
                    className="flex-grow rounded-md border px-3 py-2"
                  />
                  <label className="flex items-center gap-1">
                    <input
                      type="checkbox"
                      checked={a.isCorrect}
                      onChange={(e) =>
                        handleAnswerChangeTracNghiem(
                          a.id,
                          "isCorrect",
                          e.target.checked,
                        )
                      }
                      className="h-4 w-4"
                    />
                    Là đáp án đúng
                  </label>
                  <button
                    type="button"
                    onClick={() => removeAnswerItemTracNghiem(a.id)}
                    className="text-red-600"
                    title="Xóa đáp án"
                  >
                    <i className="fas fa-trash-alt"></i> Xóa
                  </button>
                </div>
              ))}
            </div>
          </div>

          <div>
            <label
              htmlFor="giai-thich"
              className="mb-1 block text-[13px] font-normal text-gray-700"
            >
              Giải thích đáp án đúng
            </label>
            <textarea
              id="giai-thich"
              rows={4}
              value={formData.Item.explanation}
              onChange={(e) =>
                setFormData({
                  ...formData,
                  Item: { ...formData.Item, explanation: e.target.value },
                })
              }
              className="w-full resize-none rounded-md border border-gray-300 px-3 py-2 text-[14px] focus:outline-none focus:ring-2 focus:ring-blue-500"
            ></textarea>
          </div>

          <hr className="border-gray-300" />

          <div className="flex justify-end gap-3">
            <button
              type="button"
              onClick={handlePreviewTracNghiem}
              className="flex items-center gap-2 rounded bg-orange-500 px-5 py-2 text-[14px] font-semibold text-white hover:bg-orange-600"
            >
              <i className="fas fa-eye text-lg"></i> Xem trước
            </button>
            <button
              type="button"
              onClick={handleSubmitTracNghiem}
              className="flex items-center gap-2 rounded bg-red-600 px-5 py-2 text-[14px] font-semibold text-white hover:bg-red-700"
            >
              <i className="fas fa-check text-lg"></i> Xác nhận
            </button>
          </div>
        </form>
      </div>

      {showPreview && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40">
          <div className="relative w-full max-w-3xl rounded-md bg-white p-6 shadow-lg">
            {/* Nút đóng ở góc trên bên phải */}
            <button
              onClick={() => setShowPreview(false)}
              className="absolute right-2 top-2 text-xl text-gray-600 hover:text-black"
              aria-label="Close"
            >
              <FontAwesomeIcon icon={faTimes} />
            </button>

            <h2 className="mb-4 text-lg font-semibold text-gray-800">
              Xem trước nội dung bài tập
            </h2>

            <div className="max-h-[70vh] space-y-4 overflow-y-auto text-sm text-gray-700">

              <p className="font-semibold">Danh sách câu hỏi:</p>
              <ul className="list-disc pl-5">
                {formData.Item.medias.map((q) => (
                  <li key={q.id}>
                    {{ 1: "TEXT", 2: "IMAGE", 3: "AUDIO", 4: "VIDEO" }[q.type]}:{" "}
                    {q.content || q.filePath || "[Chưa nhập]"}
                  </li>
                ))}
              </ul>
              <hr />
              <p className="font-semibold">Danh sách đáp án:</p>
              <ul className="list-disc pl-5">
                {formData.Item.answers.map((a) => (
                  <li key={a.id}>
                    {a.content || "[Chưa nhập]"} {a.isCorrect ? "(Đúng)" : ""}
                  </li>
                ))}
              </ul>
              <hr />
              <p><strong>Giải thích:</strong> {formData.Item.explanation || "[Không có]"}</p>
            </div>

            {/* Nút Đóng ở cuối modal */}
            <div className="mt-6 text-right">
              <button
                onClick={() => setShowPreview(false)}
                className="rounded bg-gray-500 px-4 py-2 text-white hover:bg-gray-600"
              >
                Đóng
              </button>
            </div>
          </div>
        </div>
      )}

    </div>
  );
}


