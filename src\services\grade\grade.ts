import axios from '@/services/axios';
import API_ROUTES from '@/constants/api-routes';
import { ApiResponse, PaginationData } from '@/types/common/api-response';
import { QueryParams } from '@/types/common/query-param';
import { GradeItem } from '@/types/grade/gradeItem';

const Grade_API = API_ROUTES.GRADE;

export const getGrade = (params: QueryParams) => {
  return axios.get<ApiResponse<PaginationData<GradeItem>>>(Grade_API, { params });
};

export const getGradeById = (id: number | string) => {
  return axios.get<ApiResponse<GradeItem>>(`${Grade_API}/${id}`);
};

export const saveGrade = (payload: Partial<GradeItem> & { id?: number }) => {
  if (payload.id) {
    return axios.put<ApiResponse<GradeItem>>(`${Grade_API}/${payload.id}`, payload);
  }
  return axios.post<ApiResponse<GradeItem>>(Grade_API, payload);
};

export const deleteGrade = (id: number | string) => {
  return axios.delete<ApiResponse<null>>(`${Grade_API}/${id}`);
};
