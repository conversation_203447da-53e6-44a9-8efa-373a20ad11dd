// services/session/session.ts

import axios from '@/services/axios';
import API_ROUTES from '@/constants/api-routes';
import { SessionItem } from '@/types/session/session';
import { ApiResponse, PaginationData } from '@/types/common/api-response';
import { QueryParams } from '@/types/common/query-param';

const session_API = API_ROUTES.SESSION;

export const getSession = (params: QueryParams) => {
  return axios.get<ApiResponse<PaginationData<SessionItem>>>(session_API, { params });
};

export const getSessionById = (id: number | string) => {
  return axios.get<ApiResponse<SessionItem>>(`${session_API}/${id}`);
};

export const saveSession = (payload: Partial<SessionItem> & { id?: number }) => {
  if (payload.id) {
    return axios.put<ApiResponse<SessionItem>>(`${session_API}/${payload.id}`, payload);
  }
  return axios.post<ApiResponse<SessionItem>>(session_API, payload);
};

export const deleteSession = (id: number | string) => {
  return axios.delete<ApiResponse<null>>(`${session_API}/${id}`);
};
