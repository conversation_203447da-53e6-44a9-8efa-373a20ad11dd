import Cookies from 'js-cookie';
import { revoketokent } from '@/services/auth';

export const logout = async () => {
  try {
    const refreshToken = Cookies.get('refreshToken') || sessionStorage.getItem('refreshToken');
    if (refreshToken) {
      await revoketokent({ token: refreshToken });
    }
  } catch (err) {
    console.error('Lỗi khi gọi revoke token:', err);
  } finally {
    // ✅ Xóa token ở cả hai nơi
    Cookies.remove('accessToken');
    Cookies.remove('refreshToken');
    sessionStorage.removeItem('accessToken');
    sessionStorage.removeItem('refreshToken');

    // ✅ Xóa thêm cờ nhớ đăng nhập
    sessionStorage.removeItem('remember');

    // ✅ (Tù<PERSON> chọn) Xóa thêm thông tin người dùng nếu có
    sessionStorage.removeItem('userInfo');
    localStorage.removeItem('userInfo');

    // ✅ Điều hướng lại trang login
    window.location.href = '/login';
  }
};
