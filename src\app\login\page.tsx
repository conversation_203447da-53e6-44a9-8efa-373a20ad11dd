"use client";

import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useRouter } from "next/navigation";
import Cookies from "js-cookie";
import { login } from "@/services/auth";

// Zod schema
const loginSchema = z.object({
  username: z.string().min(1, "Tên đăng nhập không được để trống"),
  password: z.string().min(1, "Mật khẩu không được để trống"),
  remember: z.boolean().optional(),
});

type LoginFormData = z.infer<typeof loginSchema>;

export default function LoginPage() {
  const router = useRouter();

  const {
    register,
    handleSubmit,
    setError,
    formState: { errors },
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      username: "admin",
      password: "Abc@123456",
      remember: false,
    },
  });

  const onSubmit = async (values: LoginFormData) => {
    try {
      const res = await login({
        username: values.username,
        password: values.password,
        isSetCookie: false,
      });

      const tokenData = res.data?.data;
      if (!tokenData?.token || !tokenData?.refreshToken) {
        throw new Error("Không nhận được token từ máy chủ");
      }

      const { token: accessToken, refreshToken } = tokenData;

      if (values.remember) {
        Cookies.set("accessToken", accessToken, { expires: 7 });
        Cookies.set("refreshToken", refreshToken, { expires: 7 });
        sessionStorage.setItem("remember", "true");
      } else {
        Cookies.set("accessToken", accessToken);
        Cookies.set("refreshToken", refreshToken);
        sessionStorage.setItem("remember", "false");
      }

      window.location.href = "/";
    } catch (err) {
      console.error("Đăng nhập thất bại:", err);
      setError("username", { message: "Sai thông tin đăng nhập" });
      setError("password", { message: "Sai thông tin đăng nhập" });
    }
  };

  return (
    <div
      className="relative flex min-h-screen flex-col items-center justify-center px-6 py-10 md:flex-row md:px-20 mx-auto"
      style={{ backgroundColor: "#e9083a" }}
    >
      {/* Left side */}
      <div
        className="relative mb-10 w-full max-w-md rounded-2xl bg-white bg-opacity-20 p-8 text-center md:mb-0 md:p-12 md:text-left hidden md:block"
        style={{ backdropFilter: "blur(100px)" }}
      >
        <div className="pointer-events-none absolute left-6 top-6 h-16 w-16 rounded-tl-lg border-l-4 border-t-4 border-white" />
        <div className="pointer-events-none absolute bottom-6 right-6 h-16 w-16 rounded-br-lg border-b-4 border-r-4 border-white" />
        <h2 className="mb-8 text-lg font-extrabold leading-tight text-white md:text-xl">
          QUẢN TRỊ PHẦN MỀM HỖ TRỢ <br /> ÔN TẬP CHO HỌC SINH TIỂU HỌC
        </h2>
        <img
          src="/bg_login.png"
          alt="Illustration"
          width={400}
          height={200}
          className="mx-auto"
        />
      </div>

      {/* Right side */}
      <form
        onSubmit={handleSubmit(onSubmit)}
        className="ml-md-10 md:ml-4 w-full max-w-md space-y-6 rounded-xl bg-white p-8 shadow-lg"
        autoComplete="off"
      >
        <div className="mb-8 flex items-center">
          <img
            src="/LOGO_VIETIQ.png"
            alt="Logo"
            width={100}
            height={75}
            className="ml-1"
          />
        </div>

        <h3 className="mb-4 text-center text-xl font-extrabold text-gray-800">
          Đăng nhập
        </h3>

        {/* Username */}
        <div>
          <label className="mb-1 block text-sm font-medium">
            Tên đăng nhập <span className="text-red-600">*</span>
          </label>
          <input
            {...register("username")}
            className="w-full rounded-md border px-4 py-2 focus:border-blue-300 focus:outline-none focus:ring"
            placeholder="Tên đăng nhập"
          />
          {errors.username && (
            <p className="mt-1 text-sm text-red-600">
              {errors.username.message}
            </p>
          )}
        </div>

        {/* Password */}
        <div>
          <label className="mb-1 block text-sm font-medium">
            Mật khẩu <span className="text-red-600">*</span>
          </label>
          <input
            type="password"
            {...register("password")}
            className="w-full rounded-md border px-4 py-2 focus:border-blue-300 focus:outline-none focus:ring"
            placeholder="Mật khẩu"
          />
          {errors.password && (
            <p className="mt-1 text-sm text-red-600">
              {errors.password.message}
            </p>
          )}
        </div>

        {/* Remember */}
        <div className="flex items-center space-x-2">
          <input type="checkbox" id="remember" {...register("remember")} />
          <label htmlFor="remember" className="text-sm">
            Lưu thông tin
          </label>
        </div>

        <div className="text-right text-sm">
          <a href="#" className="text-gray-600 hover:underline">
            Quên mật khẩu?
          </a>
        </div>

        <button
          type="submit"
          className="cursor-pointer w-full rounded-md bg-red-600 py-2 text-center font-semibold text-white hover:bg-red-700"
        >
          <i className="fas fa-sign-in-alt mr-2" />
          Đăng nhập
        </button>
      </form>

      {/* Bottom left */}
      <div className="absolute bottom-6 left-6 flex items-center space-x-3 text-sm font-semibold text-white">
        <i className="fas fa-headphones-alt text-xl" />
        <div>
          <div>Liên hệ CSKH</div>
          <div>0203.XXX.XXX</div>
        </div>
      </div>

      {/* SVG */}
      <svg
        aria-hidden="true"
        className="pointer-events-none absolute inset-0 h-full w-full"
        preserveAspectRatio="none"
        viewBox="0 0 1440 320"
        style={{ opacity: 0.15 }}
      >
        <path
          d="M0,96 C144,160 288,32 432,64 C576,96 720,224 864,192 C1008,160 1152,32 1296,64 C1440,96 1584,160 1728,128"
          fill="none"
          stroke="white"
          strokeWidth="3"
          transform="translate(0, 80)"
        />
        <path
          d="M0,128 C144,192 288,64 432,96 C576,128 720,256 864,224 C1008,192 1152,64 1296,96 C1440,128 1584,192 1728,160"
          fill="none"
          stroke="white"
          strokeWidth="3"
          transform="translate(0, 160)"
        />
      </svg>
    </div>
  );
}
