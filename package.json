{"name": "free-nextjs-admin-dashboard", "version": "1.3.5", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ant-design/nextjs-registry": "^1.1.0", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.1.1", "antd": "^5.26.2", "apexcharts": "^4.0.0", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "flatpickr": "^4.6.13", "heroicons": "^2.2.0", "js-cookie": "^3.0.5", "jsvectormap": "^1.6.0", "lucide-react": "^0.522.0", "next": "^14.2.4", "react": "^18", "react-apexcharts": "^1.4.1", "react-dom": "^18", "react-hook-form": "^7.58.1", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "xlsx": "^0.18.5", "zod": "^3.25.67"}, "devDependencies": {"@types/js-cookie": "^3.0.6", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.1.0", "postcss": "^8", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.5.11", "tailwindcss": "^3.4.1", "typescript": "^5"}}