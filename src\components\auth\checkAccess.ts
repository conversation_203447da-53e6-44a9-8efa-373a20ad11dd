
import { menuConfig } from "../../constants/menuConfig"; // Adjust the import path as necessary

export const hasAccessToPath = (roles: string[], path: string): boolean => {
  const check = (items: typeof menuConfig): boolean => {
    for (const item of items) {
      if (item.key === path && item.requiredRoles && item.requiredRoles.some(role => roles.includes(role))) {
        return true;
      }
      if (item.children && check(item.children)) {
        return true;
      }
    }
    return false;
  };

  return check(menuConfig);
};
