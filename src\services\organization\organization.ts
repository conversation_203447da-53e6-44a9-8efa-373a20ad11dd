import axios from '@/services/axios';
import API_ROUTES from '@/constants/api-routes';
import { OrganizationItem } from '@/types/organization/organization';
import { ApiResponse, PaginationData } from '@/types/common/api-response';
import { QueryParams } from '@/types/common/query-param';

const organization_API = API_ROUTES.ORGANIZATION;

export const getOrganization = (params: QueryParams) => {
  return axios.get<ApiResponse<PaginationData<OrganizationItem>>>(organization_API, {
    params,
  });
};

export const getAllOrganization = () => {
  return axios.get<ApiResponse<OrganizationItem[]>>(`${organization_API}/all`);
};


export const getOrganizationById = (id: number | string) => {
  return axios.get<ApiResponse<OrganizationItem>>(`${organization_API}/${id}`);
};

export const saveOrganization = (payload: Partial<OrganizationItem> & { id?: number }) => {
  if (payload.id) {
    return axios.put<ApiResponse<OrganizationItem>>(`${organization_API}/${payload.id}`, payload);
  }
  return axios.post<ApiResponse<OrganizationItem>>(organization_API, payload);
};

export const deleteOrganization = (id: number | string) => {
  return axios.delete<ApiResponse<null>>(`${organization_API}/${id}`);
};
