"use client";

import React, { useState, ReactNode } from "react";
import Link from "next/link";
import Image from "next/image";
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  UploadOutlined,
  UserOutlined,
  VideoCameraOutlined,
} from "@ant-design/icons";
import { Button, Layout, Menu, Drawer, theme } from "antd";
import Breadcrumb from "../Breadcrumb";
import DarkModeSwitcher from "../Header/DarkModeSwitcher";
import DropdownNotification from "../Header/DropdownNotification";
import DropdownMessage from "../Header/DropdownMessage";
import DropdownUser from "../Header/DropdownUser";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useGrantedRoles } from '@/hooks/useGrantedRoles';
import ProtectedMenuItem from '@/components/ProtectedMenuItem';
import { menuConfig, MenuItemConfig } from "../../constants/menuConfig"; // Adjust the import path as necessary
import {
  faBook,
  faCalendarAlt,
  faCogs,
  faEdit,
  faHeadphones,
  faListSquares,
  faSchool,
  faSchoolCircleExclamation,
  faSun,
  faCloud,
  faTowerBroadcast,
  faUsersGear,
} from "@fortawesome/free-solid-svg-icons";
import { useRouter, usePathname } from "next/navigation";

const { Header, Sider, Content } = Layout;

interface AdminLayoutProps {
  children: ReactNode;
}

const AdminLayout: React.FC<AdminLayoutProps> = ({ children }) => {
  const router = useRouter();
  const currentPath = usePathname();
  const [collapsed, setCollapsed] = useState(false);
  const [mobileOpen, setMobileOpen] = useState(false); // 👈 responsive state


  const { roles, loading } = useGrantedRoles();
  const {
    token: { colorBgContainer, borderRadiusLG },
  } = theme.useToken();

  if (loading) {
    return <div>Loading menu...</div>; // hoặc spinner
  }


  const hasAccess = (requiredRoles?: string[]) => {
    if (!requiredRoles || requiredRoles.length === 0) return true;
    return requiredRoles.some(role => roles.includes(role));
  };

  const renderMenuItems = (items: MenuItemConfig[]): React.ReactNode[] => {
    return items
      .map((item) => {
        // Render children trước để kiểm tra xem có gì để hiển thị không
        const childItems = item.children ? renderMenuItems(item.children) : [];

        const isAccessible = hasAccess(item.requiredRoles);
        const hasVisibleChildren = childItems.length > 0;

        // Nếu có con hợp lệ, render cha + con
        if (hasVisibleChildren) {
          return (
            <Menu.SubMenu
              key={item.key}
              icon={<FontAwesomeIcon icon={item.icon} />}
              title={item.label}
            >
              {childItems}
            </Menu.SubMenu>
          );
        }

        // Nếu là mục đơn lẻ và có quyền → render
        if (!item.children && isAccessible) {
          return (
            <Menu.Item
              key={item.key}
              icon={item.icon ? <FontAwesomeIcon icon={item.icon} /> : null}
            >
              {item.label}
            </Menu.Item>
          );
        }

        // Trường hợp còn lại: không hiển thị
        return null;
      })
      .filter(Boolean); // loại bỏ null
  };

  const menuItems = (
    <Menu
      theme="light"
      mode="inline"
      selectedKeys={[currentPath]}
      onClick={({ key }) => {
        setMobileOpen(false);
        router.push(key);
      }}
    >
      {renderMenuItems(menuConfig)}
    </Menu>
  );


  return (
    <Layout className="min-h-screen">
      {/* Sidebar cho desktop */}
      <Sider
        width={250}
        collapsible
        collapsed={collapsed}
        onCollapse={(value) => setCollapsed(value)}
        theme="light"
        breakpoint="lg"
        className="hidden lg:inline-block p-2"
      >
        <div className="flex h-[100px] items-center justify-center">
          <img src="/LOGO_VIETIQ.png" alt="Logo" className="h-20" />
        </div>
        <Menu
          theme="light"
          mode="inline"
          selectedKeys={[currentPath]}
          items={menuItems}
          onClick={({ key }) => {
            setMobileOpen(false);
            router.push(key);
          }}
        />
      </Sider>

      {/* Drawer cho mobile */}
      <Drawer
        title={
          <div className="flex items-center justify-center">
            <img src="/LOGO_VIETIQ.png" alt="Logo" className="h-12" />
          </div>
        }
        placement="left"
        closable
        onClose={() => setMobileOpen(false)}
        open={mobileOpen}
        styles={{ body: { padding: 0 } }}
      >
        <Menu
          theme="light"
          mode="inline"
          selectedKeys={[currentPath]}
          items={menuItems}
          onClick={({ key }) => {
            setMobileOpen(false);
            router.push(key);
          }}
        />
      </Drawer>

      <Layout>
        {/* Header */}
        <Header className="sticky top-0 z-50 flex w-full items-center justify-between bg-white px-4 py-2 shadow-sm">
          <Button
            type="text"
            icon={<MenuUnfoldOutlined />}
            className="lg:hidden" // chỉ hiện mobile
            onClick={() => setMobileOpen(true)}
            style={{
              fontSize: "20px",
              width: 48,
              height: 48,
            }}
          />
          <Button
            type="text"
            icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
            className="hidden lg:inline-flex" // chỉ hiện desktop
            onClick={() => setCollapsed(!collapsed)}
            style={{
              fontSize: "16px",
              width: 64,
              height: 64,
            }}
          />
          <div className="flex items-center gap-2 sm:gap-4 lg:hidden">
            <Link className="block flex-shrink-0" href="/">
              <Image width={32} height={32} src="/LOGO_VIETIQ.png" alt="Logo" />
            </Link>
          </div>
          <div className="mr-4 flex items-center gap-3">
            <ul className="flex items-center gap-4">
              <div className="hidden items-center space-x-3 sm:flex">
                <FontAwesomeIcon icon={faHeadphones} fontSize={24} />
                <div className="text-gray-600">
                  <div className="text-xs">Liên hệ CSKH</div>
                  <div className="text-sm font-semibold">024 XXXX XXXX</div>
                </div>
              </div>
              {/* <DarkModeSwitcher /> */}
              <DropdownNotification />
              <DropdownMessage />
            </ul>
            <DropdownUser />
          </div>
        </Header>

        {/* Nội dung chính */}
        <Content
          style={{
            margin: "5px 5px",
            padding: 10,
            minHeight: 280,
            background: colorBgContainer,
            borderRadius: 0,
          }}
        >
          <Breadcrumb />
          {children}
        </Content>
      </Layout>
    </Layout>
  );
};

export default AdminLayout;
