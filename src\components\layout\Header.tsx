'use client';

import { useState } from 'react';
import { logout } from '@/services/logout';

export default function Header() {
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [showNotifications, setShowNotifications] = useState(false);
  const [showMobileMenu, setShowMobileMenu] = useState(false);

  return (
    <header className="flex justify-between items-center px-4 md:px-8 py-4 bg-white shadow">
      {/* Logo */}
      <div className="flex items-center space-x-2">
        <img src="/LOGO_VIETIQ.png" alt="Logo" className="w-16 h-14 md:w-20 md:h-16" />
      </div>

      {/* Desktop menu */}
      <div className="hidden md:flex items-center space-x-6 text-[#1a1a1a] text-sm relative">
        {/* CSKH */}
        <div className="flex items-center space-x-2">
          <i className="fas fa-headset text-xl" />
          <div className="leading-tight">
            <div><PERSON><PERSON><PERSON> hệ <PERSON></div>
            <div className="font-extrabold text-base">024 XXXX XXXX</div>
          </div>
        </div>

        {/* Help button */}
        <button className="bg-gray-200 rounded-md w-10 h-10 flex items-center justify-center text-lg">
          <i className="fas fa-question" />
        </button>

        {/* Notifications */}
        <div className="relative">
          <button
            className="bg-gray-200 rounded-md w-10 h-10 flex items-center justify-center text-lg relative"
            onClick={() => setShowNotifications(!showNotifications)}
          >
            <i className="fas fa-bell" />
            <span className="absolute top-1 right-1 w-2.5 h-2.5 bg-red-500 rounded-full border-2 border-white" />
          </button>
          {showNotifications && (
            <div className="absolute right-0 mt-2 w-64 bg-white border rounded shadow-lg z-50">
              <div className="p-4 font-bold border-b">Thông báo</div>
              <div className="p-4 text-sm">Bạn chưa có thông báo mới.</div>
            </div>
          )}
        </div>

        {/* User menu */}
        <div className="relative">
          <button
            className="bg-gray-200 rounded-md w-10 h-10 flex items-center justify-center text-lg"
            onClick={() => setShowUserMenu(!showUserMenu)}
          >
            <i className="fas fa-user" />
          </button>
          {showUserMenu && (
            <div className="absolute right-0 mt-2 w-40 bg-white border rounded shadow-lg z-50">
              <a href="#" className="block px-4 py-2 hover:bg-gray-100 text-sm">
                Hồ sơ
              </a>
              <button
                className="block w-full text-left px-4 py-2 hover:bg-gray-100 text-sm"
                onClick={logout}
              >
                Đăng xuất
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Mobile menu button */}
      <div className="md:hidden">
        <button
          className="text-xl p-2"
          onClick={() => setShowMobileMenu(!showMobileMenu)}
        >
          <i className="fas fa-bars" />
        </button>
      </div>

      {/* Mobile dropdown menu */}
      {showMobileMenu && (
        <div className="absolute top-20 right-4 w-64 bg-white border rounded shadow-lg z-50 md:hidden">
          <div className="p-4 border-b">
            <div className="flex items-center space-x-2">
              <i className="fas fa-headset text-lg" />
              <div className="leading-tight">
                <div className="text-sm">Liên hệ CSKH</div>
                <div className="font-bold text-base">024 XXXX XXXX</div>
              </div>
            </div>
          </div>
          <div className="p-4 border-b flex items-center space-x-2">
            <i className="fas fa-bell text-lg" />
            <span>Thông báo</span>
          </div>
          <a href="#" className="block px-4 py-2 hover:bg-gray-100 text-sm">
            Hồ sơ
          </a>
          <button
            className="block w-full text-left px-4 py-2 hover:bg-gray-100 text-sm"
            onClick={logout}
          >
            Đăng xuất
          </button>
        </div>
      )}
    </header>
  );
}
