// services/Class/Class.ts

import axios from '@/services/axios';
import API_ROUTES from '@/constants/api-routes';
import { ClassItem } from '@/types/class/class';
import { ApiResponse, PaginationData } from '@/types/common/api-response';
import { QueryParams } from '@/types/common/query-param';

const Class_API = API_ROUTES.CLASS;

export const getClass = (params: QueryParams) => {
  return axios.get<ApiResponse<PaginationData<ClassItem>>>(Class_API, { params });
};

export const getClassById = (id: number | string) => {
  return axios.get<ApiResponse<ClassItem>>(`${Class_API}/${id}`);
};

export const saveClass = (payload: Partial<ClassItem> & { id?: number }) => {
  if (payload.id) {
    return axios.put<ApiResponse<ClassItem>>(`${Class_API}/${payload.id}`, payload);
  }
  return axios.post<ApiResponse<ClassItem>>(Class_API, payload);
};

export const deleteClass = (id: number | string) => {
  return axios.delete<ApiResponse<null>>(`${Class_API}/${id}`);
};
