//services/coures/course.ts
import axios from '@/services/axios';
import API_ROUTES from '@/constants/api-routes';
import { CourseItem } from '@/types/course/course';
import { ApiResponse, PaginationData } from '@/types/common/api-response';
import { QueryParams } from '@/types/common/query-param';

const COURSE_API = API_ROUTES.COURSE;

export const getCourse = (params: QueryParams) => {
  return axios.get<ApiResponse<PaginationData<CourseItem>>>(COURSE_API, {
    params,
  });
};

export const getCourseById = (id: number | string) => {
  return axios.get<ApiResponse<CourseItem>>(`${COURSE_API}/${id}`);
};

export const saveCourse = (payload: Partial<CourseItem> & { id?: number }) => {
  if (payload.id) {
    return axios.put<ApiResponse<CourseItem>>(`${COURSE_API}/${payload.id}`, payload);
  }
  return axios.post<ApiResponse<CourseItem>>(COURSE_API, payload);
};

export const deleteCourse = (id: number | string) => {
  return axios.delete<ApiResponse<null>>(`${COURSE_API}/${id}`);
};
