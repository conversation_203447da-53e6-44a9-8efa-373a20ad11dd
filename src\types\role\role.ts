
export type RoleItem = {
  id: number;
  name: string;
  code: string;
  normalizedName: string;
  description: string;
  isDefault:boolean;
};


export type SaveRoleItem = {
  id?: number;
  name: string;
  description: string;
  permissionNames: string[];
  isThrowException?: boolean;
};

export interface GrantedRole {
  roleName: string;
  isGranted: boolean;
}

export interface UpdateRolePayload {
  name: string;
  description: string;
  permissionNames: string[];
  isThrowException: boolean;
}

export interface PermissionTreeItem {
  name: string;
  isGranted: boolean;
  isInherited: boolean;
  children: PermissionTreeItem[];
}

export interface AssignPermissionRequest {
  permissionNames: string[];
}