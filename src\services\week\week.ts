// services/week/week.ts

import axios from '@/services/axios';
import API_ROUTES from '@/constants/api-routes';
import { WeekItem } from '@/types/week/week';
import { ApiResponse, PaginationData } from '@/types/common/api-response';
import { QueryParams } from '@/types/common/query-param';

const Week_API = API_ROUTES.WEEK;

export const getWeek = (params: QueryParams) => {
  return axios.get<ApiResponse<PaginationData<WeekItem>>>(Week_API, { params });
};

export const getWeekById = (id: number | string) => {
  return axios.get<ApiResponse<WeekItem>>(`${Week_API}/${id}`);
};

export const saveWeek = (payload: Partial<WeekItem> & { id?: number }) => {
  if (payload.id) {
    return axios.put<ApiResponse<WeekItem>>(`${Week_API}/${payload.id}`, payload);
  }
  return axios.post<ApiResponse<WeekItem>>(Week_API, payload);
};

export const deleteWeek = (id: number | string) => {
  return axios.delete<ApiResponse<null>>(`${Week_API}/${id}`);
};
