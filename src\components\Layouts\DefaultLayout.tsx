"use client";

import React, { useState, ReactNode, useEffect } from "react";
import Sidebar from "@/components/Sidebar";
import Header from "@/components/Header";
import Footer from "../layout/Footer";
import { BreadcrumbProvider } from "@/context/BreadcrumbContext";
import Breadcrumb from "@/components/Breadcrumb";
import TransitionLoader from "@/components/common/TransitionLoader";
export default function DefaultLayout({
  children,
}: {
  children: ReactNode;
}) {
  const [sidebarOpen, setSidebarOpen] = useState(true);

  useEffect(() => {
    const isMobile = window.innerWidth < 1024;
    if (isMobile) {
      setSidebarOpen(false);
    }
  }, []);

  return (
      <div className="flex">
        <TransitionLoader />
        {/* Sidebar */}
        <Sidebar sidebarOpen={sidebarOpen} setSidebarOpen={setSidebarOpen} />

        {/* Main Content */}
        <div className="relative flex flex-1 flex-col lg:ml-72.5">
          {/* Header */}
          <Header sidebarOpen={sidebarOpen} setSidebarOpen={setSidebarOpen} />

          {/* Main Content Area */}
          <main>
            <div className="mx-auto p-4 md:p-6 2xl:p-10">
              {/* ✅ Breadcrumb hiển thị đầu trang */}
              <Breadcrumb></Breadcrumb>
              {children}
            </div>
          </main>
        </div>
      </div>
      
  );
}
