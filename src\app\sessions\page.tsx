"use client";

import React, { useEffect, useState } from "react";
import DefaultLayout from "@/components/Layouts/DefaultLayout";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faPlus,
  faRedo,
  faSearch,
  faEdit,
  faTrash,
  faTimes,
  faFileExport,
  faFileImport,
  faFileExcel,
  faDownload,
} from "@fortawesome/free-solid-svg-icons";
import { getSession, deleteSession, saveSession } from "@/services/session/session";
import { SessionItem } from "@/types/session/session";
import Pagination from "@/components/common/Pagination";
import { Button, Modal, Form, Input, Select, notification } from "antd";
import { useBreadcrumb } from "@/context/BreadcrumbContext";
import { time } from "node:console";
import { useLoader } from "@/context/LoaderContext";
import * as XLSX from "xlsx";
import AdminLayout from "@/components/Layouts/AdminLayout";
import { getCourseById } from "@/services/course/course";
import { CourseItem } from "@/types/course/course";
import { getCourse } from "@/services/course/course";
import { WeekItem } from "@/types/week/week";
import { getWeek, getWeekById } from "@/services/week/week";
import { withAuthGuard } from "@/components/auth/withAuthGuard";

const initialForm: Partial<SessionItem> = {
  weekId: 0,
  sessionOrder: 0,
  sessionName: "",
};

function SessionsPage() {
  const { setLoading } = useLoader();
  const { setBreadcrumb } = useBreadcrumb();
  const [data, setData] = useState<SessionItem[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [inputSearchTerm, setInputSearchTerm] = useState("");
  const [form, setForm] = useState<Partial<SessionItem>>(initialForm);
  const [editingId, setEditingId] = useState<number | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [antdForm] = Form.useForm();
  const [selectedSessions, setSelectedSessions] = useState<number[]>([]);

  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalItems, setTotalItems] = useState(0);

  const [previewImportData, setPreviewImportData] = useState<SessionItem[]>([]);
  const [isPreviewModalOpen, setIsPreviewModalOpen] = useState(false);

  const [weekOptions, setWeekOptions] = useState<WeekItem[]>([]);




  const fetchSessions = async () => {
    try {
      setLoading(true);

      const res = await getSession({
        SearchTerm: searchTerm,
        Page: currentPage,
        ItemPerPage: pageSize,
      });

      const items = res.data.data?.items || [];
      const weekCache = new Map<number, any>();

      const itemsWithWeeks = await Promise.all(
        items.map(async (Session) => {
          if (!weekCache.has(Session.weekId)) {
            try {
              const weekRes = await getWeekById(Session.weekId);
              weekCache.set(Session.weekId, weekRes.data.data);
            } catch (err) {
              console.error(`Không lấy được tuần ID ${Session.weekId}`, err);
              weekCache.set(Session.weekId, null);
            }
          }

          return {
            ...Session,
            week: weekCache.get(Session.weekId),
          };
        })
      );

      setData(itemsWithWeeks);
      console.log('itemsWithWeeks',itemsWithWeeks);
      setTotalItems(res.data.data?.total || 0);
      setSelectedSessions([]);
    } catch (err) {
      console.error("Lỗi tải danh sách Tuần", err);
    } finally {
      setLoading(false);
    }
  };



  useEffect(() => {
    setBreadcrumb("Quản lý buổi học", "Danh sách buổi học");
  }, []);

  // Reset currentPage về 1 khi searchTerm thay đổi
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm]);

  // Debounce fetchSessions khi searchTerm, currentPage hoặc pageSize thay đổi
  useEffect(() => {
    const timeout = setTimeout(() => {
      fetchSessions();
    }, 300); // Debounce 300ms

    return () => clearTimeout(timeout); // Clear timeout nếu thay đổi trong thời gian chờ
  }, [searchTerm, currentPage, pageSize]);

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    setSearchTerm(inputSearchTerm);
    setCurrentPage(1); // Đặt lại trang đầu tiên

    // Delay gọi fetchSessions sau khi setState hoàn tất
    setTimeout(() => {
      fetchSessions(); // fetchSessions sẽ gọi theo currentPage = 1
    }, 0);
  };

  useEffect(() => {
    const fetchWeeks = async () => {
      try {
        // Replace this with the correct API call to get weeks, not courses
        // Example: const res = await getWeek({ ItemPerPage: 999 });
        // setWeekOptions(res.data?.data?.items ?? []);
        // If you do not have a getWeek API, map CourseItem to WeekItem shape as a fallback:
        const res = await getWeek({ ItemPerPage: 999 });
        console.log('res week', res);
        const weeks: WeekItem[] = (res.data?.data?.items ?? []).map((week: WeekItem) => ({
          ...week,
          weekId: week.id, // or another appropriate mapping
          sessionOrder: 0, // set a default or map if available
          sessionName: "", // set a default or map if available
        }));
        setWeekOptions(weeks);
      } catch (err) {
        console.error("Lỗi lấy danh sách tuần", err);
      }
    };
    fetchWeeks();
  }, []);

  const resetSearch = async () => {
    setSearchTerm("");
    setCurrentPage(1);
    await fetchSessions();
  };

  const handleSubmit = async () => {
    try {
      const values = await antdForm.validateFields();
      setLoading(true);
      await saveSession(editingId ? {
        ...values,
        id: editingId,
        sessionName: Number(values.sessionName),
        weekId: Number(values.weekId),
      } : {
        ...values,
        sessionName: Number(values.sessionName),
        weekId: Number(values.weekId),
      });
      setCurrentPage(1);
      await fetchSessions();
      closeModal();
    } catch (errorInfo) {
      console.log("Validation Failed:", errorInfo);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    Modal.confirm({
      title: "Bạn có chắc chắn muốn xoá?",
      okText: "Xoá",
      okType: "danger",
      cancelText: "Hủy",
      onOk: async () => {
        try {
          setLoading(true);
          await deleteSession(id);
          notification.success({ message: "Xoá thành công" });
          setCurrentPage(1);
          await fetchSessions();
        } catch {
          notification.error({ message: "Lỗi khi xoá Tuần" });
        } finally {
          setLoading(false);
        }
      },
    });
  };

  const openModal = (Session?: SessionItem) => {
    if (Session) {
      setForm(Session);
      setEditingId(Session.id);
      antdForm.setFieldsValue(Session); // ✅ Set form field values
    } else {
      setForm(initialForm);
      setEditingId(null);
      antdForm.resetFields(); // ✅ Reset fields
    }
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setForm(initialForm);
    setEditingId(null);
    setIsModalOpen(false);
  };

  const handleDeleteMultiple = async () => {
    Modal.confirm({
      title: `Bạn có chắc chắn muốn xoá ${selectedSessions.length} Tuần?`,
      okText: "Xoá",
      okType: "danger",
      cancelText: "Hủy",
      onOk: async () => {
        try {
          setLoading(true);
          for (const id of selectedSessions) {
            await deleteSession(id);
          }
          notification.success({ message: "Xoá thành công" });
          setSelectedSessions([]);
          setCurrentPage(1);
          await fetchSessions();
        } catch {
          notification.error({ message: "Lỗi khi xoá nhiều Tuần" });
        } finally {
          setLoading(false);
        }
      },
    });
  };


  const handleImportExcel = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    try {
      setLoading(true);
      const data = await file.arrayBuffer();
      const workbook = XLSX.read(data);
      const sheetName = workbook.SheetNames[0];
      const sheet = workbook.Sheets[sheetName];
      const rows: any[][] = XLSX.utils.sheet_to_json(sheet, { header: 1 });

      if (rows.length < 2) {
        notification.warning({ message: "File Excel không có dữ liệu." });
        return;
      }

      const header = rows[0].map((h: any) => typeof h === "string" ? h.trim().toLowerCase() : "");
      const weekIdx = header.indexOf("weekId");
      const sessionOrderIdx = header.indexOf("sessionOrder");
      const sessionNameIdx = header.indexOf("sessionName");

      if (weekIdx === -1 || sessionOrderIdx === -1 || sessionNameIdx === -1) {
        notification.error({ message: "File Excel thiếu cột bắt buộc: title, description hoặc SessionType" });
        return;
      }

      const previewData: SessionItem[] = [];
      for (let i = 1; i < rows.length; i++) {
        const row = rows[i];
        const weekId = row[weekIdx]?.toString().trim();
        const sessionOrder = row[sessionOrderIdx]?.toString().trim();
        const sessionName = row[sessionNameIdx]?.toString().trim();
        if (weekId && sessionOrder && sessionName) {
          previewData.push({
            id: 0,
            weekId: Number(weekId),
            sessionOrder: Number(sessionOrder),
            sessionName: sessionName,
          });
        }
      }

      if (previewData.length > 0) {
        setPreviewImportData(previewData);
        setIsPreviewModalOpen(true);
      } else {
        notification.warning({ message: "Không có dữ liệu hợp lệ để import." });
      }
    } catch (err) {
      console.error("Import error:", err);
      notification.error({ message: "Lỗi khi import file Excel" });
    } finally {
      setLoading(false);
      e.target.value = "";
    }
  };

  const confirmImport = async () => {
    try {
      setLoading(true);
      let successCount = 0;
      for (const item of previewImportData) {
        await saveSession(item);
        successCount++;
      }
      notification.success({ message: `Đã import ${successCount} Tuần thành công.` });
      setIsPreviewModalOpen(false);
      setPreviewImportData([]);
      setCurrentPage(1);
      await fetchSessions();
    } catch (err) {
      console.error("Lỗi khi lưu dữ liệu import:", err);
      notification.error({ message: "Lỗi khi lưu dữ liệu import" });
    } finally {
      setLoading(false);
    }
  };

  const handleExportExcel = () => {
    const exportData = data.map(({ weekId, sessionOrder, sessionName }) => ({
      weekId, sessionOrder, sessionName 
    }));

    const worksheet = XLSX.utils.json_to_sheet(exportData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "DanhSachBuoiHoc");

    XLSX.writeFile(workbook, "danh_sach_BuoiHoc.xlsx");
  };

  const handleRemovePreviewItem = (index: number) => {
    setPreviewImportData((prev) => prev.filter((_, i) => i !== index));
  };

  return (
    <AdminLayout>
      <div className="font-sans mx-auto rounded-md bg-white p-6">
        {/* Search + Add Form */}
        <div className="mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <form className="flex w-full sm:w-auto" onSubmit={handleSearch}>
            <div className="relative flex-grow">
              <input
                id="search"
                type="search"
                value={searchTerm}
                onChange={(e) => setInputSearchTerm(e.target.value)}
                placeholder="Tìm kiếm theo tên"
                className="w-full rounded-l-md border border-gray-300 py-2 pl-10 pr-12 text-gray-400 placeholder-gray-400 focus:border-red-600 focus:outline-none focus:ring-2 focus:ring-red-600"
              />
              <span className="absolute left-3 top-1/2 -translate-y-1/2 text-lg text-gray-400">
                <FontAwesomeIcon icon={faSearch} />
              </span>
              <button
                type="reset"
                onClick={resetSearch}
                className="absolute right-0 top-1/2 -translate-y-1/2 rounded-r-md border-l border-gray-300 bg-white px-3 py-2 text-gray-600 hover:text-gray-900"
              >
                <FontAwesomeIcon icon={faTimes} />
              </button>
            </div>
            <button
              type="submit"
              className="ml-2 flex items-center justify-center rounded-md bg-red-600 px-4 py-2 text-white hover:bg-red-700"
            >
              <FontAwesomeIcon icon={faSearch} />
            </button>
          </form>

          <div className="flex items-end gap-2">
            {selectedSessions.length > 0 && (
              <>
                <Button
                  danger
                  onClick={handleDeleteMultiple}
                  className="rounded-none"
                >
                  Xoá đã chọn ({selectedSessions.length})
                </Button>
              </>
            )}

            <Button
              className="rounded-none bg-blue-600 text-white"
              type="primary"
              icon={<FontAwesomeIcon icon={faPlus} />}
              onClick={() => openModal()}
            >
              Thêm buổi
            </Button>
            <input
              type="file"
              accept=".xlsx, .xls"
              id="excelInput"
              style={{ display: "none" }}
              onChange={handleImportExcel}
            />

            <Button
              className="rounded-none bg-success text-white"
              onClick={() => document.getElementById("excelInput")?.click()}
              icon={<FontAwesomeIcon icon={faFileExcel} />}
            >
              Nhập từ Excel
            </Button>

            <Button
              className="rounded-none  bg-success text-white"
              onClick={handleExportExcel}
              icon={<FontAwesomeIcon icon={faDownload} />}
            >
              Xuất Excel
            </Button>
          </div>
        </div>

        {/* Table */}
        <table className="w-full table-auto border-collapse text-sm">
          <thead>
            <tr className="bg-gray-200 font-semibold text-gray-900">
              <th className="w-[60px] border px-2 py-3 text-center">
                <div className="flex items-center justify-center gap-1">
                  <input
                    type="checkbox"
                    checked={selectedSessions.length === data.length}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedSessions(data.map((item) => item.id));
                      } else {
                        setSelectedSessions([]);
                      }
                    }}
                    className="accent-red-600"
                  />
                </div>
              </th>
              <th className="border px-4 py-3">Tên buổi</th>
              <th className="border px-4 py-3">Buổi số</th>
              <th className="border px-4 py-3">Tên tuần</th>
              <th className="w-[100px] border px-2 py-3 text-right">
                Chức năng
              </th>
            </tr>
          </thead>
          <tbody>
            {data.map((item, index) => (
              <tr
                key={item.id}
                className={`${index % 2 === 0 ? "bg-white" : "bg-gray-50"} hover:bg-red-50`}
              >
                <td className="w-[60px] border px-2 py-3 text-center">
                  <div className="flex items-center justify-center gap-1">
                    <span className="mr-4 text-xs font-bold">
                      {(currentPage - 1) * pageSize + index + 1}
                    </span>
                    <input
                      type="checkbox"
                      checked={selectedSessions.includes(item.id)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedSessions([...selectedSessions, item.id]);
                        } else {
                          setSelectedSessions(
                            selectedSessions.filter((id) => id !== item.id),
                          );
                        }
                      }}
                      className="accent-red-600"
                    />
                  </div>
                </td>
                <td className="border px-4 py-3 font-semibold">{item.sessionName}</td>
                <td className="border px-4 py-3 font-semibold">{item.sessionOrder}</td>
                <td className="border px-4 py-3 font-semibold">{item.week?.title}</td>
                <td className="border px-2 py-3 text-right">
                  <div className="flex justify-end gap-2">
                    <button
                      onClick={() => openModal(item)}
                      className="text-blue-600 hover:text-blue-800"
                      title="Sửa"
                    >
                      <FontAwesomeIcon icon={faEdit} />
                    </button>
                    <button
                      onClick={() => handleDelete(item.id)}
                      className="text-red-600 hover:text-red-800"
                      title="Xoá"
                    >
                      <FontAwesomeIcon icon={faTrash} />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
        <Pagination
          currentPage={currentPage}
          pageSize={pageSize}
          totalItems={totalItems}
          onPageChange={(page) => setCurrentPage(page)}
          onPageSizeChange={(size) => {
            setPageSize(size);
            setCurrentPage(1); // reset về trang 1 khi đổi size
          }}
        />
      </div>

      <Modal
        title={editingId ? "Cập nhật buổi học" : "Thêm buổi học"}
        open={isModalOpen}
        onCancel={closeModal}
        onOk={handleSubmit}
         footer={[
    <Button key="back" onClick={closeModal}>
      Hủy
    </Button>,
    <Button
      key="submit"
      type="primary"
      className="bg-blue-500 hover:bg-blue-600 text-white"
      onClick={handleSubmit}
    >
      {editingId ? "Cập nhật" : "Thêm mới"}
    </Button>,
  ]}
      >
        <Form layout="vertical" form={antdForm}>
          <Form.Item
            label="Tuần"
            name="weekId"
            rules={[{ required: true, message: "Vui lòng chọn tuần" }]}
          >
            <Select placeholder="Chọn tuần">
              {weekOptions.map((week) => (
                <Select.Option key={week.id} value={week.id}>
                  {week.learningObjectives}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item
            label="Tên buổi"
            name="sessionName"
            rules={[{ required: true, message: "Vui lòng nhập tên buổi" }]}
          >
            <Input placeholder="Nhập tên buổi" />
          </Form.Item>

          <Form.Item
            label="Buổi số"
            name="sessionOrder"
            rules={[{ required: true, message: "Vui lòng nhập buổi số" }]}
          >
            <Input type="number" placeholder="Nhập buổi số" />
          </Form.Item>

        </Form>
      </Modal>
      <Modal
        open={isPreviewModalOpen}
        onCancel={() => setIsPreviewModalOpen(false)}
        onOk={confirmImport}
        okText="Lưu dữ liệu"
        cancelText="Huỷ"
        width={1200}
        title="Duyệt danh sách buổi từ file Excel"
      >
        <div style={{ maxHeight: "60vh", overflowY: "auto" }}>
          <table className="w-full table-auto border-collapse text-sm">
            <thead>
              <tr className="bg-gray-200 font-semibold text-gray-900">
                <th className="border px-4 py-2">#</th>
                <th className="border px-4 py-2">Tuần Id</th>
                <th className="border px-4 py-2">Tên buổi học</th>
                <th className="border px-4 py-2">Thứ tự buổi - buổi số</th>
                <th className="border px-4 py-2">Thao tác</th>
              </tr>
            </thead>
            <tbody>
              {previewImportData.map((item, index) => (
                <tr key={index} className="hover:bg-red-50">
                  <td className="border px-4 py-2 text-center">{index + 1}</td>
                  <td className="border px-4 py-2">{item.weekId}</td>
                    <td className="border px-4 py-2">{item.sessionOrder}</td>
                  <td className="border px-4 py-2">{item.sessionName}</td>
                  <td className="border px-4 py-2 text-center">
                    <Button
                      danger
                      size="small"
                      onClick={() => handleRemovePreviewItem(index)}
                    >
                      Xoá
                    </Button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </Modal>
    </AdminLayout>
  );
}


export default withAuthGuard(SessionsPage);