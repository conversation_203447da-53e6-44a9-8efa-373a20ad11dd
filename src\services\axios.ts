import axios from 'axios';
import Cookies from 'js-cookie';
import { refreshtokent } from '@/services/auth';

let isRefreshing = false;
let failedQueue: any[] = [];

const processQueue = (error: any, token: string | null = null) => {
  failedQueue.forEach((prom) => {
    if (error) {
      prom.reject(error);
    } else {
      prom.resolve(token);
    }
  });
  failedQueue = [];
};

const instance = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL,
  withCredentials: true, // đảm bảo cookie được gửi kèm
});

const getAccessToken = () => {
  return Cookies.get('accessToken');
};

instance.interceptors.request.use((config) => {
  const token = getAccessToken();
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

instance.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest._retry) {
      if (isRefreshing) {
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject });
        }).then((token) => {
          originalRequest.headers.Authorization = `Bearer ${token}`;
          return axios(originalRequest);
        });
      }

      originalRequest._retry = true;
      isRefreshing = true;

      const refreshToken = Cookies.get('refreshToken');

      try {
        const res = await refreshtokent({ token: refreshToken!, isSetCookie: false });
        const newAccessToken = res.data?.data?.token;

        if (!newAccessToken) {
          throw new Error('Không lấy được access token khi refresh');
        }

        const remember = sessionStorage.getItem('remember') === 'true';

        if (remember) {
          Cookies.set('accessToken', newAccessToken, { expires: 7 });
        } else {
          Cookies.set('accessToken', newAccessToken); // session cookie
        }

        processQueue(null, newAccessToken);
        originalRequest.headers.Authorization = `Bearer ${newAccessToken}`;
        return axios(originalRequest);
      } catch (err) {
        processQueue(err, null);
        Cookies.remove('accessToken');
        Cookies.remove('refreshToken');
        sessionStorage.clear(); // Xóa mọi dấu vết
        window.location.href = '/login';
        return Promise.reject(err);
      } finally {
        isRefreshing = false;
      }
    }

    return Promise.reject(error);
  }
);

export default instance;
