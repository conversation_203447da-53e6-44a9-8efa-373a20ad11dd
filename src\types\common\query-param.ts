export type QueryParams = {
  Page?: number;                    // Trang hiện tại (1-based index)
  ItemPerPage?: number;            // Số lượng item trên mỗi trang
  SearchTerm?: string;             // Từ khóa tìm kiếm
  SearchColumn?: string;           // Cột muốn tìm kiếm
  SearchType?: number;             // 0: ch<PERSON>h xác, 1: gầ<PERSON> đún<PERSON> (ví dụ), v.v.
  OrderColumn?: string;            // Cột dùng để sắp xếp
  Direction?: 0 | 1 | null;               // 0: ASC, 1: DESC
  HasSearchTerm?: boolean | null;         // Có áp dụng tìm kiếm không
  SearchTermPattern?: string;      // Mẫu tìm kiếm (ví dụ: LIKE '%a%')
};