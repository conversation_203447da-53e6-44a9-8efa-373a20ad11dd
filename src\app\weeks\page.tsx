"use client";

import React, { useEffect, useState } from "react";
import DefaultLayout from "@/components/Layouts/DefaultLayout";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faPlus,
  faRedo,
  faSearch,
  faEdit,
  faTrash,
  faTimes,
  faFileExport,
  faFileImport,
  faFileExcel,
  faDownload,
} from "@fortawesome/free-solid-svg-icons";
import { getWeek, deleteWeek, saveWeek } from "@/services/week/week";
import { WeekItem } from "@/types/week/week";
import Pagination from "@/components/common/Pagination";
import { Button, Modal, Form, Input, Select, notification } from "antd";
import { useBreadcrumb } from "@/context/BreadcrumbContext";
import { time } from "node:console";
import { useLoader } from "@/context/LoaderContext";
import * as XLSX from "xlsx";
import AdminLayout from "@/components/Layouts/AdminLayout";
import { getCourseById } from "@/services/course/course";
import { CourseItem } from "@/types/course/course";
import { getCourse } from "@/services/course/course";
import { withAuthGuard } from "@/components/auth/withAuthGuard";

const initialForm: Partial<WeekItem> = {
  courseId: 0,
  weekNumber: 0,
  learningObjectives: "",
};

function WeeksPage() {
  const { setLoading } = useLoader();
  const { setBreadcrumb } = useBreadcrumb();
  const [data, setData] = useState<WeekItem[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [inputSearchTerm, setInputSearchTerm] = useState("");
  const [form, setForm] = useState<Partial<WeekItem>>(initialForm);
  const [editingId, setEditingId] = useState<number | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [antdForm] = Form.useForm();
  const [selectedWeeks, setSelectedWeeks] = useState<number[]>([]);

  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalItems, setTotalItems] = useState(0);

  const [previewImportData, setPreviewImportData] = useState<WeekItem[]>([]);
  const [isPreviewModalOpen, setIsPreviewModalOpen] = useState(false);

  const [courseOptions, setCourseOptions] = useState<CourseItem[]>([]);




  const fetchWeeks = async () => {
    try {
      setLoading(true);

      const res = await getWeek({
        SearchTerm: searchTerm,
        Page: currentPage,
        ItemPerPage: pageSize,
      });

      const items = res.data.data?.items || [];
      const courseCache = new Map<number, any>();

      const itemsWithCourses = await Promise.all(
        items.map(async (week) => {
          if (!courseCache.has(week.courseId)) {
            try {
              const courseRes = await getCourseById(week.courseId);
              courseCache.set(week.courseId, courseRes.data.data); // ✅ Chỉ lấy course thực sự
            } catch (err) {
              console.error(`Không lấy được khoá học ID ${week.courseId}`, err);
              courseCache.set(week.courseId, null);
            }
          }

          return {
            ...week,
            course: courseCache.get(week.courseId),
          };
        })
      );

      setData(itemsWithCourses);
      setTotalItems(res.data.data?.total || 0);
      setSelectedWeeks([]);
    } catch (err) {
      console.error("Lỗi tải danh sách Tuần", err);
    } finally {
      setLoading(false);
    }
  };



  useEffect(() => {
    setBreadcrumb("Quản lý Tuần", "Danh sách Tuần");
  }, []);

  // Reset currentPage về 1 khi searchTerm thay đổi
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm]);

  // Debounce fetchWeeks khi searchTerm, currentPage hoặc pageSize thay đổi
  useEffect(() => {
    const timeout = setTimeout(() => {
      fetchWeeks();
    }, 300); // Debounce 300ms

    return () => clearTimeout(timeout); // Clear timeout nếu thay đổi trong thời gian chờ
  }, [searchTerm, currentPage, pageSize]);

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    setSearchTerm(inputSearchTerm);
    setCurrentPage(1); // Đặt lại trang đầu tiên

    // Delay gọi fetchWeeks sau khi setState hoàn tất
    setTimeout(() => {
      fetchWeeks(); // fetchWeeks sẽ gọi theo currentPage = 1
    }, 0);
  };

  useEffect(() => {
    const fetchCourses = async () => {
      try {
        const res = await getCourse({ ItemPerPage:999 }); // hàm trả về danh sách CourseItem[]
        console.log('res course',res);
        setCourseOptions(res.data?.data?.items ?? []);
      } catch (err) {
        console.error("Lỗi lấy danh sách khoá học", err);
      }
    };
    fetchCourses();
  }, []);

  const resetSearch = async () => {
    setSearchTerm("");
    setCurrentPage(1);
    await fetchWeeks();
  };

  const handleSubmit = async () => {
    try {
      const values = await antdForm.validateFields();
      setLoading(true);
      await saveWeek(editingId ? {
        ...values,
        id: editingId,
        weekNumber: Number(values.weekNumber),
        courseId: Number(values.courseId),
      } : {
        ...values,
        weekNumber: Number(values.weekNumber),
        courseId: Number(values.courseId),
      });
      setCurrentPage(1);
      await fetchWeeks();
      closeModal();
    } catch (errorInfo) {
      console.log("Validation Failed:", errorInfo);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    Modal.confirm({
      title: "Bạn có chắc chắn muốn xoá?",
      okText: "Xoá",
      okType: "danger",
      cancelText: "Hủy",
      onOk: async () => {
        try {
          setLoading(true);
          await deleteWeek(id);
          notification.success({ message: "Xoá thành công" });
          setCurrentPage(1);
          await fetchWeeks();
        } catch {
          notification.error({ message: "Lỗi khi xoá Tuần" });
        } finally {
          setLoading(false);
        }
      },
    });
  };

  const openModal = (Week?: WeekItem) => {
    if (Week) {
      setForm(Week);
      setEditingId(Week.id);
      antdForm.setFieldsValue(Week); // ✅ Set form field values
    } else {
      setForm(initialForm);
      setEditingId(null);
      antdForm.resetFields(); // ✅ Reset fields
    }
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setForm(initialForm);
    setEditingId(null);
    setIsModalOpen(false);
  };

  const handleDeleteMultiple = async () => {
    Modal.confirm({
      title: `Bạn có chắc chắn muốn xoá ${selectedWeeks.length} Tuần?`,
      okText: "Xoá",
      okType: "danger",
      cancelText: "Hủy",
      onOk: async () => {
        try {
          setLoading(true);
          for (const id of selectedWeeks) {
            await deleteWeek(id);
          }
          notification.success({ message: "Xoá thành công" });
          setSelectedWeeks([]);
          setCurrentPage(1);
          await fetchWeeks();
        } catch {
          notification.error({ message: "Lỗi khi xoá nhiều Tuần" });
        } finally {
          setLoading(false);
        }
      },
    });
  };


  const handleImportExcel = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    try {
      setLoading(true);
      const data = await file.arrayBuffer();
      const workbook = XLSX.read(data);
      const sheetName = workbook.SheetNames[0];
      const sheet = workbook.Sheets[sheetName];
      const rows: any[][] = XLSX.utils.sheet_to_json(sheet, { header: 1 });

      if (rows.length < 2) {
        notification.warning({ message: "File Excel không có dữ liệu." });
        return;
      }

      const header = rows[0].map((h: any) => typeof h === "string" ? h.trim().toLowerCase() : "");
      const titleIdx = header.indexOf("title");
      const descIdx = header.indexOf("description");
      const typeIdx = header.indexOf("Weektype");

      if (titleIdx === -1 || descIdx === -1 || typeIdx === -1) {
        notification.error({ message: "File Excel thiếu cột bắt buộc: title, description hoặc WeekType" });
        return;
      }

      const previewData: WeekItem[] = [];
      for (let i = 1; i < rows.length; i++) {
        const row = rows[i];
        const courseId = row[titleIdx]?.toString().trim();
        const weekNumber = row[descIdx]?.toString().trim();
        const title = row[titleIdx]?.toString().trim();
        const learningObjectives = row[typeIdx]?.toString().trim();
        if (courseId && weekNumber && learningObjectives) {
          previewData.push({
            id: 0,
            title:title,
            courseId: Number(courseId),
            weekNumber: Number(weekNumber),
            learningObjectives
          });
        }
      }

      if (previewData.length > 0) {
        setPreviewImportData(previewData);
        setIsPreviewModalOpen(true);
      } else {
        notification.warning({ message: "Không có dữ liệu hợp lệ để import." });
      }
    } catch (err) {
      console.error("Import error:", err);
      notification.error({ message: "Lỗi khi import file Excel" });
    } finally {
      setLoading(false);
      e.target.value = "";
    }
  };

  const confirmImport = async () => {
    try {
      setLoading(true);
      let successCount = 0;
      for (const item of previewImportData) {
        await saveWeek(item);
        successCount++;
      }
      notification.success({ message: `Đã import ${successCount} Tuần thành công.` });
      setIsPreviewModalOpen(false);
      setPreviewImportData([]);
      setCurrentPage(1);
      await fetchWeeks();
    } catch (err) {
      console.error("Lỗi khi lưu dữ liệu import:", err);
      notification.error({ message: "Lỗi khi lưu dữ liệu import" });
    } finally {
      setLoading(false);
    }
  };

  const handleExportExcel = () => {
    const exportData = data.map(({ courseId, weekNumber, learningObjectives }) => ({
      courseId, weekNumber, learningObjectives
    }));

    const worksheet = XLSX.utils.json_to_sheet(exportData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "DanhSachTuan");

    XLSX.writeFile(workbook, "danh_sach_tuan.xlsx");
  };

  const handleRemovePreviewItem = (index: number) => {
    setPreviewImportData((prev) => prev.filter((_, i) => i !== index));
  };

  return (
    <AdminLayout>
      <div className="font-sans mx-auto rounded-md bg-white p-6">
        {/* Search + Add Form */}
        <div className="mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <form className="flex w-full sm:w-auto" onSubmit={handleSearch}>
            <div className="relative flex-grow">
              <input
                id="search"
                type="search"
                value={searchTerm}
                onChange={(e) => setInputSearchTerm(e.target.value)}
                placeholder="Tìm kiếm theo tên"
                className="w-full rounded-l-md border border-gray-300 py-2 pl-10 pr-12 text-gray-400 placeholder-gray-400 focus:border-red-600 focus:outline-none focus:ring-2 focus:ring-red-600"
              />
              <span className="absolute left-3 top-1/2 -translate-y-1/2 text-lg text-gray-400">
                <FontAwesomeIcon icon={faSearch} />
              </span>
              <button
                type="reset"
                onClick={resetSearch}
                className="absolute right-0 top-1/2 -translate-y-1/2 rounded-r-md border-l border-gray-300 bg-white px-3 py-2 text-gray-600 hover:text-gray-900"
              >
                <FontAwesomeIcon icon={faTimes} />
              </button>
            </div>
            <button
              type="submit"
              className="ml-2 flex items-center justify-center rounded-md bg-red-600 px-4 py-2 text-white hover:bg-red-700"
            >
              <FontAwesomeIcon icon={faSearch} />
            </button>
          </form>

          <div className="flex items-end gap-2">
            {selectedWeeks.length > 0 && (
              <>
                <Button
                  danger
                  onClick={handleDeleteMultiple}
                  className="rounded-none"
                >
                  Xoá đã chọn ({selectedWeeks.length})
                </Button>
              </>
            )}

            <Button
              className="rounded-none bg-blue-600 text-white"
              type="primary"
              icon={<FontAwesomeIcon icon={faPlus} />}
              onClick={() => openModal()}
            >
              Thêm Tuần
            </Button>
            <input
              type="file"
              accept=".xlsx, .xls"
              id="excelInput"
              style={{ display: "none" }}
              onChange={handleImportExcel}
            />

            <Button
              className="rounded-none bg-success text-white"
              onClick={() => document.getElementById("excelInput")?.click()}
              icon={<FontAwesomeIcon icon={faFileExcel} />}
            >
              Nhập từ Excel
            </Button>

            <Button
              className="rounded-none  bg-success text-white"
              onClick={handleExportExcel}
              icon={<FontAwesomeIcon icon={faDownload} />}
            >
              Xuất Excel
            </Button>
          </div>
        </div>

        {/* Table */}
        <table className="w-full table-auto border-collapse text-sm">
          <thead>
            <tr className="bg-gray-200 font-semibold text-gray-900">
              <th className="w-[60px] border px-2 py-3 text-center">
                <div className="flex items-center justify-center gap-1">
                  <input
                    type="checkbox"
                    checked={selectedWeeks.length === data.length}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedWeeks(data.map((item) => item.id));
                      } else {
                        setSelectedWeeks([]);
                      }
                    }}
                    className="accent-red-600"
                  />
                </div>
              </th>
              <th className="border px-4 py-3">Môn học</th>
              <th className="border px-4 py-3">Tên tuần</th>
              <th className="border px-4 py-3">Tuần số</th>
              <th className="border px-4 py-3">Mục tiêu học tập</th>
              <th className="w-[100px] border px-2 py-3 text-right">
                Chức năng
              </th>
            </tr>
          </thead>
          <tbody>
            {data.map((item, index) => (
              <tr
                key={item.id}
                className={`${index % 2 === 0 ? "bg-white" : "bg-gray-50"} hover:bg-red-50`}
              >
                <td className="w-[60px] border px-2 py-3 text-center">
                  <div className="flex items-center justify-center gap-1">
                    <span className="mr-4 text-xs font-bold">
                      {(currentPage - 1) * pageSize + index + 1}
                    </span>
                    <input
                      type="checkbox"
                      checked={selectedWeeks.includes(item.id)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedWeeks([...selectedWeeks, item.id]);
                        } else {
                          setSelectedWeeks(
                            selectedWeeks.filter((id) => id !== item.id),
                          );
                        }
                      }}
                      className="accent-red-600"
                    />
                  </div>
                </td>
                <td className="border px-4 py-3 font-semibold">{item.course?.title}</td>
                 <td className="border px-4 py-3 font-semibold">{item.title}</td>
                <td className="border px-4 py-3">{item.weekNumber}</td>
                <td className="border px-4 py-3 font-semibold">
                  {item.learningObjectives}
                </td>
                <td className="border px-2 py-3 text-right">
                  <div className="flex justify-end gap-2">
                    <button
                      onClick={() => openModal(item)}
                      className="text-blue-600 hover:text-blue-800"
                      title="Sửa"
                    >
                      <FontAwesomeIcon icon={faEdit} />
                    </button>
                    <button
                      onClick={() => handleDelete(item.id)}
                      className="text-red-600 hover:text-red-800"
                      title="Xoá"
                    >
                      <FontAwesomeIcon icon={faTrash} />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
        <Pagination
          currentPage={currentPage}
          pageSize={pageSize}
          totalItems={totalItems}
          onPageChange={(page) => setCurrentPage(page)}
          onPageSizeChange={(size) => {
            setPageSize(size);
            setCurrentPage(1); // reset về trang 1 khi đổi size
          }}
        />
      </div>

      <Modal
        title={editingId ? "Cập nhật Tuần" : "Thêm Tuần"}
        open={isModalOpen}
        onCancel={closeModal}
        onOk={handleSubmit}
        footer={[
            <Button key="back" onClick={closeModal}>
              Hủy
            </Button>,
            <Button
              key="submit"
              type="primary"
              className="bg-blue-500 hover:bg-blue-600 text-white"
              onClick={handleSubmit}
            >
              {editingId ? "Cập nhật" : "Thêm mới"}
            </Button>,
          ]}
      >
        <Form layout="vertical" form={antdForm}>
          <Form.Item
            label="Môn học"
            name="courseId"
            rules={[{ required: true, message: "Vui lòng chọn môn học" }]}
          >
            <Select placeholder="Chọn môn học">
              {courseOptions.map((course) => (
                <Select.Option key={course.id} value={course.id}>
                  {course.title}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item
            label="Tên tuần"
            name="Title"
            rules={[{ required: true, message: "Vui lòng nhập tên tuần" }]}
          >
            <Input placeholder="Nhập tên tuần" />
          </Form.Item>

          <Form.Item
            label="Tuần số"
            name="weekNumber"
            rules={[{ required: true, message: "Vui lòng nhập số tuần" }]}
          >
            <Input type="number" placeholder="Nhập số tuần" />
          </Form.Item>

          <Form.Item
            label="Mục tiêu học tập"
            name="learningObjectives"
            rules={[{ required: true, message: "Vui lòng nhập mục tiêu học tập" }]}
          >
            <Input.TextArea rows={3} placeholder="Nhập mục tiêu học tập" />
          </Form.Item>
        </Form>
      </Modal>
      <Modal
        open={isPreviewModalOpen}
        onCancel={() => setIsPreviewModalOpen(false)}
        onOk={confirmImport}
        okText="Lưu dữ liệu"
        cancelText="Huỷ"
        width={1200}
        title="Duyệt danh sách Tuần từ file Excel"
      >
        <div style={{ maxHeight: "60vh", overflowY: "auto" }}>
          <table className="w-full table-auto border-collapse text-sm">
            <thead>
              <tr className="bg-gray-200 font-semibold text-gray-900">
                <th className="border px-4 py-2">#</th>
                <th className="border px-4 py-2">Môn học</th>
                <th className="border px-4 py-2">Tên tuần số</th>
                <th className="border px-4 py-2">Tuần số</th>
                <th className="border px-4 py-2">Mục tiêu</th>
                <th className="border px-4 py-2">Thao tác</th>
              </tr>
            </thead>
            <tbody>
              {previewImportData.map((item, index) => (
                <tr key={index} className="hover:bg-red-50">
                  <td className="border px-4 py-2 text-center">{index + 1}</td>
                  <td className="border px-4 py-2">{item.courseId}</td>
                    <td className="border px-4 py-2">{item.title}</td>
                  <td className="border px-4 py-2">{item.weekNumber}</td>
                  <td className="border px-4 py-2">{item.learningObjectives}</td>
                  <td className="border px-4 py-2 text-center">
                    <Button
                      danger
                      size="small"
                      onClick={() => handleRemovePreviewItem(index)}
                    >
                      Xoá
                    </Button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </Modal>

    </AdminLayout>
  );
}

export default withAuthGuard(WeeksPage);
