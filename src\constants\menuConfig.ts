// src/constants/menuConfig.ts
import {
  faListSquares,
  faSchool,
  faUsersGear,
  faCalendarAlt,
  faSun,
  faBook,
  faEdit,
  faCogs,
} from "@fortawesome/free-solid-svg-icons";

export interface MenuItemConfig {
  key: string;
  label: string;
  icon?: any;
  requiredRoles?: string[];
  children?: MenuItemConfig[];
}

export const menuConfig: MenuItemConfig[] = [
  {
    key: "sub-danh-muc",
    label: "<PERSON>h mục",
    icon: faListSquares,
    children: [
      {
        key: "/organizations",
        label: "Tổ chức - Trường",
        icon: faSchool,
        requiredRoles: ["admin"],
      },
      {
        key: "/grades",
        label: "<PERSON>ấp lớp học",
        icon: faUsersGear,
        requiredRoles: ["admin"],
      },
      {
        key: "/classes",
        label: "Lớ<PERSON>",
        icon: faUsersGear,
        requiredRoles: ["admin"],
      },
      {
        key: "/courses",
        label: "<PERSON>ô<PERSON> học",
        icon: faListSquares,
        requiredRoles: ["admin"],
      },
      {
        key: "/weeks",
        label: "Tuần",
        icon: faCalendarAlt,
        requiredRoles: ["admin"],
      },
      {
        key: "/sessions",
        label: "Buổi",
        icon: faSun,
        requiredRoles: ["admin"],
      },
      {
        key: "/lessions",
        label: "Bài học",
        icon: faBook,
        requiredRoles: ["admin"],
      },
      {
        key: "/exercises",
        label: "Chương trình học",
        icon: faEdit,
        requiredRoles: ["admin"],
      },
    ],
  },
  {
    key: "sub-system",
    label: "Hệ thống",
    icon: faCogs,
    requiredRoles: ["admin"],
    children: [
      {
        key: "/user",
        label: "User",
        requiredRoles: ["admin"],
      },
    ],
  },
];
