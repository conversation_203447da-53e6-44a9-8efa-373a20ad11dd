
//lib/auth.ts

import axios from './axios';
import { LoginRequest, RefreshTokentRequest, RegisterRequest, RevokeTokentRequest } from '@/types/auth';

export const login = (data: LoginRequest) => {
  return axios.post('/auth/login', data);
};

export const register = (data: RegisterRequest) => {
  return axios.post('/auth/register', data);
};

export const refreshtokent = (data: RefreshTokentRequest) => {
  return axios.post('/auth/refresh-token', data);
};

export const revoketokent = (data: RevokeTokentRequest) => {
  return axios.post('/auth/revoke-token', data);
};

