

import axios from '@/services/axios';
import API_ROUTES from '@/constants/api-routes';
import { AssignPermissionRequest, GrantedRole, PermissionTreeItem, RoleItem, SaveRoleItem, UpdateRolePayload } from '@/types/role/role';
import { ApiResponse, PaginationData } from '@/types/common/api-response';
import { QueryParams } from '@/types/common/query-param';

const Role_API = API_ROUTES.ROLE;

export const getRole = (params: QueryParams) => {
  return axios.get<ApiResponse<PaginationData<RoleItem>>>(Role_API, { params });
};

export const getAllRoles = () => {
  return axios.get<ApiResponse<RoleItem[]>>(`${Role_API}/all`);
};

export const getRoleById = (id: number | string) => {
  return axios.get<ApiResponse<RoleItem>>(`${Role_API}/${id}`);
};

export const getGrantedRolesForCurrentUser = () => {
  return axios.get<ApiResponse<GrantedRole[]>>(`${API_ROUTES.ROLE}/me/granted`);
};

export const getGrantedRolesByRoleId = (roleId: number | string) => {
  return axios.get<ApiResponse<GrantedRole[]>>(`${API_ROUTES.ROLE}/${roleId}/granted`);
};

export const updateRole = (id: number | string, payload: UpdateRolePayload) => {
  return axios.put<ApiResponse<null>>(`${API_ROUTES.ROLE}/${id}`, payload);
};

export const setRoleAsDefault = (id: number | string) => {
  return axios.patch<ApiResponse<null>>(`${API_ROUTES.ROLE}/${id}/default`);
};


export const saveRole = (payload: Partial<SaveRoleItem> & { id?: number }) => {
  const data = {
    name: payload.name ?? '',
    description: payload.description ?? '',
    permissionNames: payload.permissionNames ?? [],
    isThrowException: payload.isThrowException ?? true,
  };

  if (payload.id) {
    return axios.put<ApiResponse<RoleItem>>(`${Role_API}/${payload.id}`, data);
  }
  return axios.post<ApiResponse<RoleItem>>(Role_API, data);
};

export const deleteRole = (id: number | string) => {
  return axios.delete<ApiResponse<null>>(`${Role_API}/${id}`);
};


//Role Permissions

export const getRolePermissionTree = (roleId: number | string) => {
  return axios.get<ApiResponse<PermissionTreeItem[]>>(`${API_ROUTES.PERMISSION}/role/${roleId}/tree`);
};

export const assignPermissionsToRole = (roleId: number | string, payload: AssignPermissionRequest) => {
  return axios.post<ApiResponse<null>>(`${API_ROUTES.PERMISSION}/role/${roleId}`, payload);
};
